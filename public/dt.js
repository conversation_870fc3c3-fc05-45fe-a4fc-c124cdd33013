/**
 * @description: 初始化灯塔配置
 * @param {*}
 */
function initDT(env) {
  // 初始化配置
  window.dt &&
  window.dt.set({
      // 项目id, 由灯塔项目组统一分配，申请链接：http://arms.fee.lianjia.com/doc/client/#%E7%99%BB%E5%BD%95%E7%B3%BB%E7%BB%9F
      pid: 'xxxx',
      // 用户ucid, 用于发生异常时追踪用户信息/计算系统用户数. 一般在cookie中可以取到(lianjia_uuid), 没有可传空字符串
      ucid: (window.userInfo && window.userInfo.userCode) || '',
      // 必填 "production" | "testing" | "preview"，其他配置会被当成无效日志，不入库
      env: env === 'production' ? 'production' : 'testing',
      // [可选]业务方的js版本号, 会随着打点数据一起上传, 方便区分数据来源
      // 可以不填, 默认为1.0.0
      // 【建议】对应业务迭代的版本号
      version: '1.0.0',
      // 非必填 设备id, 建议取cookie中lianjia_uuid, 不填则由sdk自行生成
      uuid: '',
      // [可选]在这里控制具体的上报数据种类, 可以按需关闭
      record: {
        // 是否为单页应用，默认false 【1.4.0新增，用于修正业务pv统计不准确的问题】
        spa: false,
        // 是否监控用户在线时长数据, 默认为true
        time_on_page: true,
        // 是否监控页面载入性能, 默认为true
        performance: true,
        //  是否监控页面报错信息, 默认为true
        js_error: true,
        // 配置需要监控的页面报错类别, 仅在js_error为true时生效, 默认均为true(可以将配置改为false, 以屏蔽不需要上报的错误类别)
        js_error_report_config: {
          // js运行时报错
          ERROR_RUNTIME: true,
          // js资源加载失败
          ERROR_SCRIPT: true,
          // css资源加载失败
          ERROR_STYLE: true,
          // 图片资源加载失败
          ERROR_IMAGE: true,
          // 音频资源加载失败
          ERROR_AUDIO: true,
          // 视频资源加载失败
          ERROR_VIDEO: true,
          // vue运行时报错
          ERROR_CONSOLE: true,
          // 未catch错误
          ERROR_TRY_CATCH: true,
          // 自定义检测函数, 上报前最后判断是否需要报告该错误
          // 回调函数说明
          // 传入参数 =>
          //            desc:  字符串, 错误描述
          //            stack: 字符串, 错误堆栈信息
          //            reason:  字符串, 仅错误类型为 unhandledrejection 时传入该参数 【1.4.2-0、@lianjia/fee-sdk@dev新增】
          //            message: 字符串, 仅错误类型为 unhandledrejection 时传入该参数 【1.4.2-0、@lianjia/fee-sdk@dev新增】
          // 返回值 =>
          //            true  : 上报打点请求
          //            false : 不需要上报
          // eslint-disable-next-line max-params
          checkErrorNeedReport(desc, stack, reason, message) {
            return true;
          },
        },
        // 接口请求监控配置 【1.4.0新增，用于统计接口请求状况】
        api_report_config: {
          // 是否启用，默认为true
          enable: true,
          // 接口请求日志是否上报请求体
          withBody: false,
          // 接口请求日志是否上报响应体
          withResp: false,
          // 数据采样率，默认为1
          sampleRate: 1,
          /* 接口请求过滤方法
              *
              * params 格式如下
                  {
                  url: '', // api url，
                  status: 200, // http status
                  response: '' // api 的响应，字符串
                  }
              */
          reportFilter(param) {
            const customParams = param;
            // 返回值可以是 false 或者 Object 对象。返回false 则此次api数据直接丢弃
            return customParams;
          },
        },
        webVitals: true,
        white_screen: {
          // 需要监控的 Dom选择器, 默认项目的根节点是<div id="root"></div>， 如果是class名请填写.root, 默认取第一个dom节点
          target: '#root',
          // load事件触发后等待的毫秒数，用于等待router的异步chunk加载
          // wait_ms: 2000,
          // dom树稳定的毫秒数，灯塔会在dom树稳定 stable_ms 之后进行检测
          // stable_ms: 3000,
          // 如果dom树一直没有变化，则会在 timeout_ms 后强制执行一次检测
          // timeout_ms: 5000,
        },
      },
      // [可选]将不同url归并为同一类页面
      // 对于如同
      // xxx.com/detail/1.html
      // xxx.com/detail/2.html
      // xxx.com/detail/3.html
      // ...
      // 这种页面来说, 虽然url不同, 但他们本质上是同一个页面
      // 因此需要业务方传入一个处理函数, 根据当前url解析出真实的页面类型(例如: 二手房列表/经纪人详情页), 以便灯塔系统对错误来源进行分类
      // 回调函数说明
      // 传入参数 => window.location
      // 返回值 => 对应的的页面类型(50字以内, 建议返回汉字, 方便查看), 默认是返回当前页面的url
      getPageType(location) {
        return `页面：${location.host}${location.pathname}`;
      },
    });
}
