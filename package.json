{"name": "ke-react-template", "version": "1.0.0", "description": "", "main": "index.jsx", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build:production": "cross-env NODE_ENV=production webpack -c webpack/webpack.prod.js", "build:test": "cross-env NODE_ENV=test webpack -c webpack/webpack.prod.js", "build:preview": "cross-env NODE_ENV=preview webpack -c webpack/webpack.prod.js", "start": "cross-env NODE_ENV=development webpack serve -c webpack/webpack.dev.js", "prepare": "husky", "lint-staged": "lint-staged"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint"]}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@swc/core": "^1.6.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.1.0", "eslint-config-standard-with-typescript": "^43.0.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-n": "^16.6.2", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-promise": "^6.6.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^4.6.2", "html-webpack-plugin": "^5.6.0", "husky": "^9.1.6", "less": "^4.2.0", "less-loader": "^12.2.0", "lint-staged": "^13.3.0", "mini-css-extract-plugin": "^2.9.0", "prettier": "^3.3.3", "progress-bar-webpack-plugin": "^2.1.0", "sass": "^1.77.5", "sass-loader": "^14.2.1", "style-loader": "^4.0.0", "stylus": "^0.63.0", "stylus-loader": "^8.1.0", "swc-loader": "^0.2.6", "terser-webpack-plugin": "^5.3.10", "typescript": "^5.6.3", "webpack": "^5.91.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.0.4", "webpack-merge": "^5.10.0"}, "dependencies": {"@ant-design/icons": "^5.3.7", "@types/react": "^18.3.3", "antd": "^5.18.1", "antd-mobile": "^5.36.1", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.26.2"}}