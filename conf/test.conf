server {
    #默认配置。如果不想用默认的，可以修改
    listen 8080;
    root /data0/www/htdocs/code;
    access_log /data0/www/logs/access.log main;
    error_log /data0/www/logs/error.log;

    server_name http://sparks.ttb.test.ke.com; #后端测试环境域名
    
    #按以下写法配，可以解决单页应用的路由跳转回根目录的问题
    location / {
        index index.html;
        # history模式最重要就是这里
        try_files $uri /index.html;
        add_header Cache-Control 'no-cache, no-store, must-revalidate';
    }
    #location /demo { # 多页应用，每个页面配置一个路由（不需要可删除）
    #   index demo.html; # index为固定属性不改动，demo.html为打包产物html名称
    #   try_files $uri /demo.html;
    #   add_header Cache-Control 'no-cache, no-store, must-revalidate';
    #}

    #接口转发
    location /api { #业务后端接口转发
        proxy_pass $server_name;
        rewrite ^/api/(.*)$ /$1 break;
    }
    #keboot登录组件提供的固定接口，可根据需要取消注释
    # location /loginUser/info { 
    #     proxy_pass $server_name;
    #     rewrite ^/$ /$1 break;
    # }
    # location /login/cas {
    #     proxy_pass $server_name;
    #     rewrite ^/$ /$1 break;
    # }
    # location /logout { #keboot组件提供的退出登录接口
    #     proxy_pass $server_name;
    #     rewrite ^/$ /$1 break;
    # }


}