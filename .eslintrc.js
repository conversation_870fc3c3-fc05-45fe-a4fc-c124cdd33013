module.exports = {
  env: {
    browser: true,
    es2021: true
  },
  extends: [
    'airbnb', // 使用airbnb的eslint规则
    'plugin:react/recommended',
    'prettier'
  ],
  overrides: [
    {
      files: ['*.jsx', '*.js'] // todo-ts,tsx
    }
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true
    }
  },
  settings: {
    react: {
      version: 'detect' // 自动检测 React 版本
    },
    'import/resolver': {
      node: {
        extensions: ['.js', '.jsx'] // todo-'.ts', '.tsx'
      }
    }
  },
  plugins: ['react', 'prettier'],
  rules: {
    'prettier/prettier': 'error',
    'implicit-arrow-linebreak': 'off',
    'import/no-unresolved': 'off', // 不检测引用问题
    'react/react-in-jsx-scope': 'off', // 不校验未显式引用React
    'react/display-name': 'off', // React组件定义时允许使用匿名函数
    'react/no-array-index-key': 'warn', // 数组使用index作为key时警告
    'jsx-a11y/label-has-associated-control': ['error', {
      'required': {
        'some': ['nesting', 'id']
      }
    }], // 修复表单标签关联检查的误报
    'class-methods-use-this': 'off', // 允许类方法不使用this
    'no-console': 'warn' // console语句改为警告而不是错误
  }
};
