# 项目名称

本项目为基于 webpack5 打包的 react + static_web 项目，内置了打包、登录（后端接入登录，前端做-310未登录重定向处理）、路由、代码格式化、灯塔接入等配置，后续我们将持续优化，欢迎给我们提供反馈和宝贵的建议！👏👏👏

# 资料：

## 技术资料：

- <!--项目文档工具-->

### 技术栈：

React + webpack5 + antd@5 / antd-mobile@5 + eslint + prettier + husky
⚠️：推荐 node@16 + npm@8

### 云平台：

-

### 浮屠：

-

### 项目结构：

|-- project
|  |-- scripts
|  | |-- build <!--构建脚本-->
|  | | |-- test.sh
|  | | |-- preview.sh
|  | | |-- prod.sh
|  |-- public
|  | |-- dt.js <!--灯塔初始化配置，新项目请申请灯塔并根据自己的项目完善pid、uuid、ucid等-->
|  | |-- index.html <!--html模板-->
|  |-- src
|  | |-- config <!--全局配置-->
|  | | |-- routes.jsx
|  | |-- components
|  | | |-- commonComponents <!--通用类组件，主要作为antd/antd-mobile补充-->
|  | | |-- businessComponents <!--业务组件-->
|  | | |-- compViewerPage.jsx <!--组件效果预览处，新增组件时手动新增至该文件-->
|  | |-- assets <!--图片等资源-->
|  | |-- pages <!--页面文件夹-->
|  | |-- utils
|  | | |-- const.js <!--常量定义-->
|  | | |-- api <!--请求api-->
|  | | |-- responseHandler.js <!--接口返回处理：是否登录重定向-->
|  | |-- index.jsx <!--入口js文件-->
|  | |-- index.css <!--入口css文件-->
|  |-- webpack <!--打包配置-->
|  | |-- webpack.base.js
|  | |-- webpack.dev.js
|  | |-- webpack.prod.js
|  |-- conf <!--ngix配置-->
|  | |-- test.conf
|  | |-- preview.conf
|  | |-- production.conf
|  |-- .husky
|  | |-- pre-commit
|  |-- .vscode
|  | |-- setting.json
|  |-- .eslintrc.js <!--eslint配置文件-->
|  |-- .npmrc <!--npm源配置文件-->
|  |-- .prettierrc.js <!--prettier配置文件-->
|  |-- package-lock.json
|  |-- package.json
|  |-- readme.md

# 本地启动

## 初始化

```bash
npm i
```

## 启动服务

```bash
npm start
```

## 本地开发

switch 开启本地代理： 127.0.0.1 dev.rule.ke.com
服务访问地址：http://dev.rule.ke.com:8080/

## 构建脚本

（ps:可直接复制粘贴在青蝉的构建脚本中）
测试环境：sh scripts/build/test.sh
预发环境：sh scripts/build/preview.sh
线上环境：sh scripts/build/production.sh

# 维护人员

## FE

-

## RD

-

## QA

-

## PM

-
