<!DOCTYPE html>
<html>
<head>
    <title>API测试</title>
</head>
<body>
    <h1>API测试页面</h1>
    <button onclick="testPersonalLeaderboard()">测试个人排行榜</button>
    <button onclick="testTeamLeaderboard()">测试团队排行榜</button>
    <div id="result"></div>

    <script type="module">
        import apiService from './src/utils/api/services.js';
        
        window.testPersonalLeaderboard = async () => {
            try {
                console.log('测试个人排行榜...');
                const result = await apiService.getLeaderboard('personal');
                console.log('个人排行榜结果:', result);
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
            } catch (error) {
                console.error('个人排行榜错误:', error);
                document.getElementById('result').innerHTML = '错误: ' + error.message;
            }
        };
        
        window.testTeamLeaderboard = async () => {
            try {
                console.log('测试团队排行榜...');
                const result = await apiService.getLeaderboard('team');
                console.log('团队排行榜结果:', result);
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
            } catch (error) {
                console.error('团队排行榜错误:', error);
                document.getElementById('result').innerHTML = '错误: ' + error.message;
            }
        };
    </script>
</body>
</html>
