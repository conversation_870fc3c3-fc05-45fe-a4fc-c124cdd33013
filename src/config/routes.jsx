import DefaultContent from 'src/pages/commonPage/defaultContent';
import ErrorBoundary from 'src/pages/commonPage/errorBoundary';
import Main from 'src/pages/main';
import PageDemo1 from 'src/pages/pageDemo1';
import PageDemo2 from 'src/pages/pageDemo2';
import CompViewerPage from 'src/components/compViewerPage';
import BaoMing from 'src/pages/baoming';

const menuRoutes = [
  {
    path: '/pageDemo1',
    Component: PageDemo1
  }
];

const routerRoutes = [
  {
    path: '/',
    Component: Main,
    ErrorBoundary, // 错误处理组件
    children: [
      // 使用children&在Main组件中使用<Outlet />标签展示子组件，适用于使用layout布局的项目（如有 header+menu+content 的pc端管理后台）
      {
        index: true,
        element: <DefaultContent /> // 匹配父级路由/时默认展示该页面
      },
      ...menuRoutes
    ]
  },
  {
    // 路由直接放一级，适用于无layout布局的项目（如通常的h5项目，但需要挨个配置ErrorBoundary）
    path: '/pageDemo2',
    ErrorBoundary,
    Component: PageDemo2
  },
  {
    path: '/compViewerPage',
    ErrorBoundary,
    Component: CompViewerPage
  },
  {
    path: '/baoming',
    ErrorBoundary,
    Component: BaoMing
  },
  {
    path: '/index',
    ErrorBoundary,
    Component: Main
  }
];
export { routerRoutes, menuRoutes };
