/* 主页样式 */
.main-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏样式 */
.navbar {
  background-color: #ffffff;
  border-bottom: 1px solid #e5e5e5;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

.navbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 4rem;
}

.navbar-logo {
  font-size: 1.25rem;
  font-weight: bold;
  color: #111827;
}

.navbar-nav {
  display: none;
  gap: 2rem;
}

.navbar-nav.desktop {
  display: flex;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  background: none;
  border: none;
  cursor: pointer;
  text-decoration: none;
  color: #4b5563;
}

.nav-item:hover {
  color: #111827;
  background-color: #f3f4f6;
  transform: translateY(-1px);
}

.nav-item.active {
  background-color: #dbeafe;
  color: #1d4ed8;
}

.nav-item.nav-cta {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
}

.nav-item.nav-cta:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  color: white;
  transform: translateY(-2px);
}

.nav-item-icon {
  margin-right: 0.5rem;
}

.mobile-menu-button {
  display: block;
  padding: 0.5rem;
  color: #4b5563;
  background: none;
  border: none;
  cursor: pointer;
}

.mobile-menu-button:hover {
  color: #111827;
}

.mobile-menu {
  display: block;
  padding: 1rem 0;
  border-top: 1px solid #e5e7eb;
}

.mobile-menu .nav-item {
  width: 100%;
  justify-content: flex-start;
  margin-bottom: 0.25rem;
}

/* 主要内容区域 */
.main-content {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 1rem;
  padding-top: 80px; /* 为固定导航栏留出空间 */
}

/* 页面标题区域 */
.header-section {
  padding: 2rem 0;
  text-align: center;
  background-color: #ffffff;
  margin-bottom: 2rem;
}

.page-header {
  max-width: 800px;
  margin: 0 auto;
}

.page-title {
  font-size: 2rem;
  font-weight: 600;
  color: #333333;
  margin: 0;
}

/* 快速导航卡片 */
.quick-nav-section {
  margin-bottom: 3rem;
}

.quick-nav-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  max-width: 1000px;
  margin: 0 auto;
}

.nav-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e5e5;
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  border-color: #3b82f6;
}

.nav-card-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border-radius: 8px;
}

.nav-card-content {
  flex: 1;
}

.nav-card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.nav-card-desc {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

.nav-card-arrow {
  font-size: 1.25rem;
  color: #9ca3af;
  transition: all 0.3s ease;
}

.nav-card:hover .nav-card-arrow {
  color: #3b82f6;
  transform: translateX(4px);
}

/* 团队分享安排 */
.team-schedule-section {
  background: #ffffff;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 3rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e5e5;
}

.section-header {
  text-align: center;
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.schedule-container {
  max-width: 100%;
}

.schedule-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-tabs {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.filter-tab {
  padding: 0.5rem 1rem;
  border: 1px solid #e5e5e5;
  background: #ffffff;
  border-radius: 6px;
  font-size: 0.875rem;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-tab:hover {
  border-color: #3b82f6;
  color: #3b82f6;
}

.filter-tab.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: #ffffff;
}

.search-container {
  flex: 1;
  max-width: 300px;
}

.search-input-simple {
  width: 100%;
  padding: 0.5rem 1rem;
  border: 1px solid #e5e5e5;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.search-input-simple:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.schedule-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.schedule-item {
  display: flex;
  gap: 1.5rem;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.schedule-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.schedule-time {
  flex-shrink: 0;
  text-align: center;
  min-width: 80px;
}

.time-date {
  font-size: 0.875rem;
  font-weight: 600;
  color: #3b82f6;
  margin-bottom: 0.25rem;
}

.time-slot {
  font-size: 0.75rem;
  color: #6b7280;
}

.schedule-content {
  flex: 1;
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
  gap: 1rem;
}

.schedule-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  flex: 1;
}

.schedule-badges {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.simple-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.schedule-description {
  font-size: 0.875rem;
  color: #4b5563;
  line-height: 1.5;
  margin-bottom: 0.75rem;
}

.schedule-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
  color: #6b7280;
}

.schedule-speaker {
  font-weight: 500;
}

.schedule-attendance {
  color: #059669;
  font-weight: 500;
}

/* 排行榜区域 */
.leaderboard-section {
  background: #ffffff;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 3rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e5e5;
}

.simple-leaderboard {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.leaderboard-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.leaderboard-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.rank-number {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #3b82f6;
  color: #ffffff;
  border-radius: 50%;
  font-weight: 600;
  font-size: 1rem;
}

.leaderboard-item:first-child .rank-number {
  background: #f59e0b;
}

.leaderboard-item:nth-child(2) .rank-number {
  background: #6b7280;
}

.leaderboard-item:nth-child(3) .rank-number {
  background: #cd7c2f;
}

.user-avatar {
  font-size: 2rem;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  border-radius: 50%;
  border: 2px solid #e5e5e5;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.user-team {
  font-size: 0.875rem;
  color: #6b7280;
}

.user-stats {
  text-align: right;
}

.points {
  font-size: 1rem;
  font-weight: 600;
  color: #059669;
  margin-bottom: 0.25rem;
}

.contributions {
  font-size: 0.875rem;
  color: #6b7280;
}

/* 徽章样式 */
.simple-badge.type-ai {
  background: #dbeafe;
  color: #1d4ed8;
}

.simple-badge.type-frontend {
  background: #d1fae5;
  color: #059669;
}

/* 所有分享列表样式 - 紧凑版 */
.all-shares-section {
  background: #ffffff;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

.all-shares-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

/* 超紧凑的分享项目设计 */
.ultra-compact-item {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  border-left: 2px solid #3b82f6;
  transition: all 0.15s ease;
  margin-bottom: 0.25rem;
}

.ultra-compact-item:hover {
  border-color: #d1d5db;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  transform: translateY(-0.5px);
}

/* 超紧凑的行布局 */
.item-row {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.375rem 0.5rem;
  min-height: 40px;
}

/* 日期徽章 */
.date-badge {
  flex-shrink: 0;
  font-size: 0.625rem;
  font-weight: 600;
  color: #3b82f6;
  background: #f0f9ff;
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
  min-width: 35px;
  text-align: center;
  line-height: 1.2;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  min-width: 0;
  line-height: 1.2;
}

/* 标题行 */
.title-line {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  margin-bottom: 0.125rem;
}

.title-text {
  font-size: 0.875rem;
  font-weight: 600;
  color: #111827;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.team-tag {
  font-size: 0.5rem;
  font-weight: 500;
  color: #059669;
  background: #ecfdf5;
  padding: 0.0625rem 0.25rem;
  border-radius: 2px;
  border: 1px solid #a7f3d0;
  flex-shrink: 0;
  line-height: 1;
}

/* 信息行 */
.info-line {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.speaker-info,
.type-info,
.location-info {
  font-size: 0.5rem;
  color: #6b7280;
  line-height: 1;
  white-space: nowrap;
}

.speaker-info {
  color: #3b82f6;
  font-weight: 500;
}

/* 操作按钮 */
.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: #3b82f6;
  color: #ffffff;
  border-radius: 3px;
  text-decoration: none;
  font-size: 0.75rem;
  transition: all 0.15s ease;
  flex-shrink: 0;
}

.action-btn:hover {
  background: #2563eb;
  color: #ffffff;
  transform: scale(1.1);
}

/* 超紧凑设计的响应式样式 */
@media (max-width: 768px) {
  .all-shares-section {
    padding: 0.5rem;
    margin-bottom: 1rem;
  }

  .all-shares-list {
    gap: 0.25rem;
  }

  .item-row {
    padding: 0.25rem 0.375rem;
    min-height: 36px;
    gap: 0.375rem;
  }

  .date-badge {
    font-size: 0.5rem;
    min-width: 30px;
    padding: 0.0625rem 0.125rem;
  }

  .title-text {
    font-size: 0.75rem;
  }

  .team-tag {
    font-size: 0.4375rem;
    padding: 0.0625rem 0.1875rem;
  }

  .speaker-info,
  .type-info,
  .location-info {
    font-size: 0.4375rem;
  }

  .info-line {
    gap: 0.375rem;
  }

  .action-btn {
    width: 20px;
    height: 20px;
    font-size: 0.625rem;
  }
}

/* 日历链接按钮样式 - 紧凑版 */
.calendar-link-section {
  margin-top: 0.75rem;
  text-align: center;
}

.calendar-link-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border: none;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.calendar-link-btn:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.calendar-icon {
  font-size: 0.875rem;
}

.calendar-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.125rem;
}

.calendar-title {
  font-weight: 600;
  font-size: 0.75rem;
}

.calendar-subtitle {
  font-size: 0.625rem;
  opacity: 0.9;
}

.calendar-arrow {
  font-size: 0.875rem;
  transition: transform 0.2s ease;
}

.calendar-link-btn:hover .calendar-arrow {
  transform: translateX(2px);
}

/* 优化标题样式 - 紧凑版 */
.section-header {
  margin-bottom: 0.75rem;
}

.v0-section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

/* 分享网格布局样式 - 现代化设计 */
.shares-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.share-grid-item {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  min-height: 320px;
  display: flex;
  flex-direction: column;
}

.share-grid-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

/* 卡片头部区域 */
.share-grid-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
  border-radius: 16px 16px 0 0;
}

.grid-date-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.5rem 0.875rem;
  border-radius: 20px;
  font-size: 0.8125rem;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  backdrop-filter: blur(10px);
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.grid-title {
  font-size: 1.125rem;
  font-weight: 700;
  color: #1e293b;
  margin: 1.5rem 1.5rem 1rem 1.5rem;
  line-height: 1.4;
  padding-right: 4rem; /* 为日期标签留空间 */
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.grid-meta {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  margin: 0 1.5rem 1rem 1.5rem;
  padding: 1rem;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

.grid-speaker,
.grid-team {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  flex: 1;
}

.meta-icon {
  font-size: 1rem;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 50%;
  flex-shrink: 0;
}

.meta-text {
  color: #475569;
  font-weight: 600;
  flex: 1;
}

.material-link {
  margin-left: 0.5rem;
  padding: 0.25rem 0.5rem;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  color: #0369a1;
  text-decoration: none;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  border: 1px solid #7dd3fc;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.material-link:hover {
  background: linear-gradient(135deg, #0369a1 0%, #0284c7 100%);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(3, 105, 161, 0.3);
}

.grid-description {
  font-size: 0.9375rem;
  color: #64748b;
  line-height: 1.6;
  margin: 0 1.5rem 1.5rem 1.5rem;
  padding: 1rem;
  background: linear-gradient(135deg, #fefefe 0%, #f8fafc 100%);
  border-radius: 12px;
  border-left: 4px solid #3b82f6;
  display: -webkit-box;
  -webkit-line-clamp: 10;
  -webkit-box-orient: vertical;
  overflow: hidden;
  position: relative;
  flex: 1;
}

.grid-description::before {
  content: '"';
  position: absolute;
  top: 0.5rem;
  left: 0.75rem;
  font-size: 2rem;
  color: #3b82f6;
  opacity: 0.3;
  font-family: serif;
}

.grid-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  margin: 0 1.5rem 1.5rem 1.5rem;
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
}

.grid-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  flex: 1;
}

.grid-tag {
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  border: 1px solid transparent;
  transition: all 0.2s ease;
}

.type-tag {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1e40af;
  border-color: #93c5fd;
}

.location-tag {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  color: #065f46;
  border-color: #6ee7b7;
}

.grid-action-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.8125rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.grid-action-btn:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

.section-subtitle {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.125rem;
}

/* 报名页面布局 */
.register-layout {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 2rem;
  align-items: start;
}

.register-form-section {
  min-width: 0; /* 防止内容溢出 */
}

.register-guide-section {
  position: sticky;
  top: 100px; /* 固定在顶部，留出导航栏空间 */
}

/* 报名指导说明样式 */
.register-guide {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
  border-radius: 12px;
  padding: 1.5rem;
}

.guide-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #0c4a6e;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.guide-content {
  margin-top: 1rem;
}

.guide-tips {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.tip-icon {
  font-size: 1.125rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.tip-text {
  font-size: 0.875rem;
  color: #374151;
  line-height: 1.5;
}

.tip-text strong {
  color: #1e40af;
  font-weight: 600;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .register-guide {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .guide-title {
    font-size: 1rem;
  }

  .tip-item {
    padding: 0.5rem;
    gap: 0.5rem;
  }

  .tip-text {
    font-size: 0.75rem;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .calendar-link-btn {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
    padding: 1.5rem;
  }

  .calendar-text {
    align-items: center;
  }

  .calendar-arrow {
    transform: rotate(90deg);
  }

  .calendar-link-btn:hover .calendar-arrow {
    transform: rotate(90deg) translateX(4px);
  }
}

/* 加载和错误状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.loading-container p {
  margin-top: 1rem;
  color: #6b7280;
  font-size: 0.875rem;
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  margin: 1rem 0;
}

.error-message p {
  color: #dc2626;
  margin-bottom: 1rem;
}

.error-message button {
  padding: 0.5rem 1rem;
  background: #dc2626;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background 0.2s ease;
}

.error-message button:hover {
  background: #b91c1c;
}

.no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  color: #6b7280;
}

.no-data-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-data-message p {
  font-size: 1rem;
  margin: 0;
}

.no-data-subtitle {
  font-size: 0.875rem !important;
  color: #9ca3af !important;
  margin-top: 0.5rem !important;
}

.simple-badge.type-backend {
  background: #fef3c7;
  color: #d97706;
}

.simple-badge.type-devops {
  background: #e0e7ff;
  color: #5b21b6;
}

.simple-badge.type-ai-app {
  background: #fce7f3;
  color: #be185d;
}

.simple-badge.difficulty-beginner {
  background: #d1fae5;
  color: #059669;
}

.simple-badge.difficulty-intermediate {
  background: #fef3c7;
  color: #d97706;
}

.simple-badge.difficulty-advanced {
  background: #fee2e2;
  color: #dc2626;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: 0 0.5rem;
    padding-top: 70px;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .quick-nav-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .nav-card {
    padding: 1rem;
  }

  .nav-card-icon {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }

  .schedule-filters {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .filter-tabs {
    justify-content: center;
  }

  .search-container {
    max-width: 100%;
  }

  .schedule-item {
    flex-direction: column;
    gap: 1rem;
  }

  .schedule-time {
    min-width: auto;
    text-align: left;
  }

  .schedule-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .schedule-badges {
    align-self: flex-start;
  }

  .schedule-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .leaderboard-item {
    padding: 0.75rem;
  }

  .rank-number {
    width: 35px;
    height: 35px;
    font-size: 0.875rem;
  }

  .user-avatar {
    width: 40px;
    height: 40px;
    font-size: 1.5rem;
  }

  .user-stats {
    text-align: left;
  }

  .mobile-menu-button {
    display: block;
  }

  .navbar-nav.desktop {
    display: none;
  }
}

@media (max-width: 480px) {
  .header-section {
    padding: 1rem 0;
  }

  .team-schedule-section,
  .leaderboard-section {
    padding: 1rem;
    margin-bottom: 2rem;
  }

  .section-title {
    font-size: 1.25rem;
  }

  .filter-tab {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
  }

  .schedule-item {
    padding: 1rem;
  }

  .schedule-title {
    font-size: 0.875rem;
  }

  .simple-badge {
    font-size: 0.625rem;
    padding: 0.125rem 0.375rem;
  }
}

/* 统计数据网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  max-width: 1000px;
  margin: 0 auto;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
}

.stat-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 2.5rem;
  font-weight: bold;
  color: white;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.25rem;
}

.stat-trend {
  font-size: 0.75rem;
  color: #10b981;
  font-weight: 500;
}

.content-section {
  margin-bottom: 2rem;
}

/* 分享列表区域 */
.sessions-section {
  background: white;
  padding: 4rem 1rem;
  margin-top: -2rem;
  border-radius: 2rem 2rem 0 0;
  position: relative;
  z-index: 10;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #111827;
  margin-bottom: 1rem;
}

.section-description {
  font-size: 1.125rem;
  color: #6b7280;
  max-width: 600px;
  margin: 0 auto;
}

/* 搜索和筛选 */
.filters-container {
  display: flex;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.search-box {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 1.125rem;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.75rem;
  font-size: 1rem;
  transition: all 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-group {
  display: flex;
  gap: 0.5rem;
}

.filter-select {
  padding: 0.75rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.75rem;
  font-size: 0.875rem;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
}

.filter-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 分享组 */
.sessions-group {
  margin-bottom: 3rem;
}

.group-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1.5rem;
  padding-left: 1rem;
  border-left: 4px solid #667eea;
}

.sessions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

/* 分享卡片 */
.session-card {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #f3f4f6;
  overflow: hidden;
  transition: all 0.3s ease;
}

.session-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.session-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.5rem 1.5rem 0;
}

.session-meta {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.session-date {
  font-size: 0.875rem;
  font-weight: 600;
  color: #667eea;
}

.session-time {
  font-size: 0.75rem;
  color: #6b7280;
}

.session-status {
  display: flex;
  align-items: center;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.status-upcoming {
  background-color: #fef3c7;
  color: #d97706;
}

.status-badge.status-scheduled {
  background-color: #dbeafe;
  color: #1d4ed8;
}

.session-content {
  padding: 1rem 1.5rem 1.5rem;
}

.session-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.session-badges {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.session-description {
  font-size: 0.875rem;
  color: #4b5563;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.session-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #f3f4f6;
}

.session-speaker,
.session-attendance {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.speaker-icon,
.attendance-icon {
  font-size: 1rem;
}

.speaker-name {
  font-weight: 500;
  color: #374151;
}

.attendance-count {
  font-weight: 500;
  color: #374151;
}

/* 欢迎横幅 */
.welcome-banner {
  background: linear-gradient(to right, #2563eb, #9333ea);
  border-radius: 0.5rem;
  padding: 2rem;
  color: white;
  margin-bottom: 2rem;
}

.welcome-title {
  font-size: 1.875rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.welcome-description {
  font-size: 1.125rem;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.welcome-button {
  background-color: white;
  color: #2563eb;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.welcome-button:hover {
  background-color: #f3f4f6;
}

/* 卡片网格 */
.card-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: 1fr;
}

.card-grid.three-cols {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.card-grid.two-cols {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* 卡片样式 */
.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  padding: 1.5rem;
  transition: all 0.3s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  flex: 1;
  margin-right: 1rem;
}

.card-badges {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.card-info {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  color: #4b5563;
}

.card-info-icon {
  margin-right: 0.5rem;
}

.card-description {
  font-size: 0.875rem;
  color: #374151;
  line-height: 1.5;
}

/* 徽章样式 */
.badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  text-align: center;
  white-space: nowrap;
}

.badge.type-ai {
  background-color: #dbeafe;
  color: #1d4ed8;
}

.badge.type-frontend {
  background-color: #dcfce7;
  color: #166534;
}

.badge.type-backend {
  background-color: #f3e8ff;
  color: #7c3aed;
}

.badge.type-devops {
  background-color: #fed7aa;
  color: #ea580c;
}

.badge.type-ai-app {
  background-color: #fce7f3;
  color: #be185d;
}

.badge.difficulty-beginner {
  background-color: #f0fdf4;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.badge.difficulty-intermediate {
  background-color: #fefce8;
  color: #a16207;
  border: 1px solid #fde047;
}

.badge.difficulty-advanced {
  background-color: #fef2f2;
  color: #dc2626;
  border: 1px solid #fca5a5;
}

/* 按钮样式 */
.btn {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-primary {
  background-color: #2563eb;
  color: white;
}

.btn-primary:hover {
  background-color: #1d4ed8;
}

.btn-success {
  background-color: #059669;
  color: white;
}

.btn-success:hover {
  background-color: #047857;
}

.btn-full {
  width: 100%;
}

.btn-large {
  padding: 0.75rem 2rem;
  font-size: 1rem;
}

.btn-secondary {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
  background-color: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.btn-outline {
  background-color: transparent;
  color: #667eea;
  border: 2px solid #667eea;
}

.btn-outline:hover {
  background-color: #667eea;
  color: white;
}

/* 表格样式 */
.table-container {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th {
  background-color: #f9fafb;
  padding: 0.75rem 1.5rem;
  text-align: left;
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid #e5e7eb;
}

.table td {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  font-size: 0.875rem;
}

.table tbody tr {
  transition: background-color 0.2s;
}

.table tbody tr:hover {
  background-color: #f9fafb;
}

.table tbody tr.highlight {
  background-color: #eff6ff;
}

/* 排名徽章 */
.rank-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  font-size: 0.875rem;
  font-weight: bold;
}

.rank-badge.rank-1 {
  background-color: #fbbf24;
  color: white;
}

.rank-badge.rank-2 {
  background-color: #d1d5db;
  color: white;
}

.rank-badge.rank-3 {
  background-color: #d97706;
  color: white;
}

.rank-badge.rank-other {
  background-color: #f3f4f6;
  color: #374151;
}

/* 排行榜区域 */
.leaderboard-section {
  background: #f8fafc;
  padding: 4rem 1rem;
}

/* 排行榜头部样式 */
.leaderboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
  overflow: hidden;
  position: relative;
}

.leaderboard-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
}

.v0-leaderboard-subtitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
  padding: 14px 20px;
  position: relative;
  z-index: 1;
}

/* 刷新按钮样式 - 适配页面UI */
.refresh-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  height: 28px;
  padding: 0 12px;
  border: none;
  border-radius: 14px;
  background: linear-gradient(135deg,
    rgba(74, 144, 226, 0.3) 0%,
    rgba(80, 102, 200, 0.2) 50%,
    rgba(142, 68, 173, 0.25) 100%);
  color: rgba(255, 255, 255, 0.95);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 2;
  margin: 12px 16px;
  backdrop-filter: blur(15px);
  min-width: 60px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.refresh-btn:hover {
  background: linear-gradient(135deg,
    rgba(74, 144, 226, 0.45) 0%,
    rgba(80, 102, 200, 0.35) 50%,
    rgba(142, 68, 173, 0.4) 100%);
  border-color: rgba(255, 255, 255, 0.4);
  color: #ffffff;
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 4px 16px rgba(74, 144, 226, 0.2);
}

.refresh-btn:active {
  
  transform: translateY(0) scale(1);
}

.refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: linear-gradient(135deg,
    rgba(74, 144, 226, 0.15) 0%,
    rgba(80, 102, 200, 0.1) 50%,
    rgba(142, 68, 173, 0.12) 100%);
  color: #ffffff;
  border-color: #ffffff;
 
  box-shadow: none;
}

.refresh-btn:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* 刷新按钮图标和文字样式 */
.refresh-btn svg {
  width: 14px;
  height: 14px;
  transition: transform 0.3s ease;
  flex-shrink: 0;
}

.refresh-btn:hover svg {
  transform: rotate(180deg);
}

.refresh-btn:active svg {
  transform: rotate(360deg);
}

.refresh-btn:disabled svg {
  opacity: 0.6;
}

.refresh-btn .refresh-text {
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0.5px;
  white-space: nowrap;
}

/* 加载状态动画 */
@keyframes refresh-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.refresh-btn.loading svg {
  animation: refresh-spin 1s linear infinite;
}

.refresh-btn.loading .refresh-text {
  opacity: 0.8;
}

/* 队长标记样式 */
.captain-badge {
  display: inline-block;
  margin-left: 4px;
  font-size: 14px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  animation: crown-glow 2s ease-in-out infinite alternate;
}

@keyframes crown-glow {
  0% {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  }
  100% {
    filter: drop-shadow(0 1px 4px rgba(255, 215, 0, 0.4));
  }
}

/* 团队成员样式 */
.team-members {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  align-items: center;
}

.member-name {
  display: inline-flex;
  align-items: center;
  font-size: 0.875rem;
  color: #374151;
}

.leaderboard-container {
  max-width: 1000px;
  margin: 0 auto;
}

/* 前三名展示 */
.top-three {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.podium-item {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #f3f4f6;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.podium-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.podium-item.podium-1::before {
  background: linear-gradient(90deg, #fbbf24, #f59e0b);
}

.podium-item.podium-2::before {
  background: linear-gradient(90deg, #d1d5db, #9ca3af);
}

.podium-item.podium-3::before {
  background: linear-gradient(90deg, #d97706, #b45309);
}

.podium-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);
}

.podium-avatar {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.podium-rank {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.875rem;
  color: white;
}

.podium-1 .podium-rank {
  background: #fbbf24;
}

.podium-2 .podium-rank {
  background: #d1d5db;
}

.podium-3 .podium-rank {
  background: #d97706;
}

.podium-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
}

.podium-team {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 1rem;
}

.podium-points {
  font-size: 1.5rem;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 0.25rem;
}

.podium-contributions {
  font-size: 0.875rem;
  color: #6b7280;
}

/* 排行榜表格 */
.leaderboard-table {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #f3f4f6;
}

.table-header {
  display: grid;
  grid-template-columns: 80px 1fr 100px 100px;
  background: #f8fafc;
  padding: 1rem 1.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid #e5e7eb;
}

.table-row {
  display: grid;
  grid-template-columns: 80px 1fr 100px 100px;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s;
  align-items: center;
}

.table-row:hover {
  background-color: #f9fafb;
}

.table-row.top-rank {
  background-color: #eff6ff;
}

.table-cell {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-avatar {
  font-size: 1.5rem;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
  color: #111827;
  font-size: 0.875rem;
}

.user-team {
  font-size: 0.75rem;
  color: #6b7280;
}

.points-value,
.contributions-value {
  font-weight: 600;
  color: #374151;
}

/* 标题样式 */
.section-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: #111827;
  margin-bottom: 1.5rem;
}

.page-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: #111827;
  margin-bottom: 1rem;
}

/* 占位符样式 */
.placeholder {
  text-align: center;
  padding: 3rem 0;
  color: #6b7280;
}

/* 无结果提示 */
.no-results {
  text-align: center;
  padding: 4rem 2rem;
  color: #6b7280;
}

.no-results-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-results h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.no-results p {
  font-size: 0.875rem;
  color: #6b7280;
}

/* CTA区域 */
.cta-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 4rem 1rem;
  text-align: center;
  color: white;
}

.cta-content {
  max-width: 600px;
  margin: 0 auto;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.cta-description {
  font-size: 1.125rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  line-height: 1.6;
}

.cta-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* 报名页面响应式设计 */
@media (max-width: 768px) {
  .register-layout {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .register-guide-section {
    position: static;
    order: -1; /* 在移动端将指南放在表单上方 */
  }

  .register-guide {
    padding: 1rem;
  }

  .guide-title {
    font-size: 1rem;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .register-layout {
    grid-template-columns: 1fr 300px;
    gap: 1.5rem;
  }

  .register-guide {
    padding: 1.25rem;
  }
}

/* 分享网格响应式设计 - 现代化适配 */
@media (max-width: 768px) {
  .shares-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-top: 1rem;
  }

  .share-grid-item {
    border-radius: 12px;
    min-height: 280px;
  }

  .grid-title {
    font-size: 1rem;
    margin: 1rem 1rem 0.75rem 1rem;
    padding-right: 3.5rem;
    -webkit-line-clamp: 3;
  }

  .grid-date-badge {
    top: 0.75rem;
    right: 0.75rem;
    padding: 0.375rem 0.625rem;
    font-size: 0.75rem;
  }

  .grid-meta {
    margin: 0 1rem 0.75rem 1rem;
    padding: 0.75rem;
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .grid-speaker,
  .grid-team {
    font-size: 0.8125rem;
    flex: none;
  }

  .material-link {
    margin-left: 0.375rem;
    padding: 0.1875rem 0.375rem;
    font-size: 0.6875rem;
  }

  .grid-description {
    margin: 0 1rem 1rem 1rem;
    padding: 0.75rem;
    font-size: 0.875rem;
    -webkit-line-clamp: 8;
  }

  .grid-footer {
    margin: 0 1rem 1rem 1rem;
    padding: 0.75rem;
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .grid-tags {
    justify-content: flex-start;
  }

  .grid-action-btn {
    align-self: center;
    padding: 0.5rem 1.5rem;
    font-size: 0.75rem;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .shares-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }
}

@media (min-width: 1025px) and (max-width: 1400px) {
  .shares-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1401px) {
  .shares-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .navbar-nav.desktop {
    display: none;
  }

  .mobile-menu-button {
    display: block;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .sessions-section {
    padding: 2rem 1rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .filters-container {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box {
    max-width: none;
  }

  .filter-group {
    justify-content: center;
  }

  .sessions-grid {
    grid-template-columns: 1fr;
  }

  .top-three {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .table-header,
  .table-row {
    grid-template-columns: 60px 1fr 80px 80px;
    padding: 0.75rem 1rem;
  }

  .cta-title {
    font-size: 2rem;
  }

  .cta-description {
    font-size: 1rem;
  }

  .cta-actions {
    flex-direction: column;
    align-items: center;
  }
}

@media (min-width: 768px) {
  .mobile-menu-button {
    display: none;
  }

  .mobile-menu {
    display: none;
  }
}
