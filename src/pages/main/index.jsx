import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Spin } from 'antd';
import { useLeaderboard } from '../../hooks/useApi';
// import apiService from '../../utils/api/services';
import './index.less';
import './index_v0.less';
import './no-animations.less';
import '../baoming/style.css';

// 生成真实系统日历的函数
const generateRealCalendar = (year, month, scheduleData) => {
  // 按日期分组API数据
  const eventsByDate = {};

  // 处理新的API数据结构
  const schedules = scheduleData?.schedules || scheduleData || [];

  if (schedules && schedules.length > 0) {
    schedules.forEach((item) => {
      if (item.start_date && item.end_date) {
        // 处理日期范围，为范围内的每一天添加事件
        const startDate = new Date(item.start_date);
        const endDate = new Date(item.end_date);

        // 遍历日期范围内的每一天
        const currentDate = new Date(startDate);
        while (currentDate <= endDate) {
          // 更新年份为当前年份来匹配日历显示
          const updatedDate = new Date(
            year,
            currentDate.getMonth(),
            currentDate.getDate()
          );
          const dateKey = updatedDate.toISOString().split('T')[0];

          if (!eventsByDate[dateKey]) {
            eventsByDate[dateKey] = [];
          }

          // 使用team_names数组或teams字符串
          const teams =
            item.team_names ||
            (item.teams ? item.teams.split(', ') : ['未知队伍']);
          teams.forEach((team) => {
            if (!eventsByDate[dateKey].includes(team)) {
              eventsByDate[dateKey].push(team);
            }
          });

          // 移动到下一天
          currentDate.setDate(currentDate.getDate() + 1);
        }
      }
    });
  }

  // 生成当月的真实日历
  const firstDay = new Date(year, month - 1, 1); // month是1-12，Date需要0-11

  // 计算第一天是周几（周一开始，0=周一，6=周日）
  const firstDayOfWeek = (firstDay.getDay() + 6) % 7;

  // 计算需要显示的开始日期（可能是上个月的日期）
  const startDate = new Date(firstDay);
  startDate.setDate(firstDay.getDate() - firstDayOfWeek);

  const weeks = [];
  const currentDate = new Date(startDate);

  // 生成6周的日历（42天）
  for (let week = 0; week < 6; week += 1) {
    const weekData = {
      days: [],
      hasEvents: false,
      eventTeams: ''
    };

    const weekEvents = new Set();

    // 生成一周的7天
    for (let day = 0; day < 7; day += 1) {
      const date = currentDate.getDate();
      const isCurrentMonth = currentDate.getMonth() === month - 1;
      const dateString = currentDate.toISOString().split('T')[0];

      // 检查这一天是否有分享事件
      const hasShare =
        eventsByDate[dateString] && eventsByDate[dateString].length > 0;
      if (hasShare) {
        weekData.hasEvents = true;
        eventsByDate[dateString].forEach((team) => weekEvents.add(team));
      }

      weekData.days.push({
        date,
        isCurrentMonth,
        hasShare,
        fullDate: new Date(currentDate)
      });

      currentDate.setDate(currentDate.getDate() + 1);
    }

    // 设置这一周的事件队伍信息
    if (weekEvents.size > 0) {
      weekData.eventTeams = `本周分享排班：${Array.from(weekEvents).join('、')}`;
    }

    weeks.push(weekData);
  }

  return weeks;
};

// 刷新图标组件 - 全新设计
function RefreshIcon({ className = '', size = 14 }) {
  return (
    <svg
      className={className}
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <polyline points="23 4 23 10 17 10" />
      <polyline points="1 20 1 14 7 14" />
      <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15" />
    </svg>
  );
}

RefreshIcon.propTypes = {
  className: PropTypes.string,
  size: PropTypes.number
};

RefreshIcon.defaultProps = {
  className: '',
  size: 14
};

export default function MainPage() {
  const [activeTab, setActiveTab] = useState('home');
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // API Hooks
  const {
    data: personalLeaderboard,
    loading: personalLoading,
    error: personalError,
    refresh: refreshPersonal
  } = useLeaderboard('personal', { immediate: true });

  const {
    data: teamLeaderboard,
    loading: teamLoading,
    error: teamError,
    refresh: refreshTeam
  } = useLeaderboard('team', { immediate: true });

  // 调试信息已移除

  // 动态计算卡片高度的函数
  const calculateCardHeight = (item) => {
    const titleLength = item.title?.length || 0;
    const descriptionLength = item.description?.length || 0;

    // 基础高度
    let baseHeight = 280;

    // 根据标题长度调整
    if (titleLength > 60) {
      baseHeight += 40; // 长标题增加高度
    } else if (titleLength > 30) {
      baseHeight += 20; // 中等标题增加高度
    }

    // 根据描述长度调整
    if (descriptionLength > 300) {
      baseHeight += 120; // 长描述大幅增加高度
    } else if (descriptionLength > 200) {
      baseHeight += 80; // 中长描述增加高度
    } else if (descriptionLength > 100) {
      baseHeight += 40; // 中等描述增加高度
    } else if (descriptionLength < 50) {
      baseHeight -= 40; // 短描述减少高度
    }

    // 确保最小高度
    return Math.max(baseHeight, 240);
  };

  // 动态计算行数限制的函数
  const calculateLineClamp = (content, type) => {
    const length = content?.length || 0;

    if (type === 'title') {
      if (length > 60) return 4;
      if (length > 30) return 3;
      return 2;
    }

    if (type === 'description') {
      if (length > 300) return 12;
      if (length > 200) return 10;
      if (length > 100) return 8;
      return 6;
    }

    return 3;
  };

  // 团队ID到团队显示名称的映射
  const getTeamDisplayName = (teamId) => {
    if (teamId && typeof teamId === 'number' && teamId > 0) {
      return `第${teamId}队`;
    }
    return null;
  };

  // 处理个人排行榜数据的函数
  const processPersonalLeaderboardData = (rawData) => {
    if (!rawData) return [];

    // 处理不同的数据结构
    let dataArray = [];

    if (Array.isArray(rawData)) {
      dataArray = rawData;
    } else if (rawData.data && Array.isArray(rawData.data)) {
      dataArray = rawData.data;
    } else if (rawData.results && Array.isArray(rawData.results)) {
      dataArray = rawData.results;
    } else if (rawData.members && Array.isArray(rawData.members)) {
      dataArray = rawData.members;
    } else {
      return [];
    }

    return dataArray.map((item, index) => {
      // 获取团队ID
      const teamId =
        item.team_id || item.teamId || item.group_id || item.groupId;

      // 优先使用直接的团队名称字段，如果没有则通过ID映射显示"第X队"
      const teamName =
        item.team ||
        item.team_name ||
        item.teamName ||
        item.group ||
        item.group_name ||
        item.groupName ||
        item.department ||
        item.dept ||
        item.unit ||
        (teamId ? getTeamDisplayName(teamId) : null);

      return {
        id: item.id || item.member_id || item.user_id || item.userId || index,
        userId: item.member_id || item.user_id || item.userId || item.id,
        name:
          item.name ||
          item.user_name ||
          item.username ||
          item.member_name ||
          '未知用户',
        team: teamName,
        teamId,
        points:
          item.points || item.score || item.total_score || item.totalScore || 0,
        shareCount:
          item.share_count ||
          item.shareCount ||
          item.contributions ||
          item.shares ||
          0,
        qualityCount:
          item.quality_count || item.qualityCount || item.quality_shares || 0,
        rank: item.rank || item.ranking || index + 1,
        isCaptain: item.is_captain || false
      };
    });
  };

  // 处理团队排行榜数据的函数
  const processTeamLeaderboardData = (rawData) => {
    if (!rawData) return [];

    // 处理不同的数据结构
    let dataArray = [];

    if (Array.isArray(rawData)) {
      dataArray = rawData;
    } else if (rawData.data && Array.isArray(rawData.data)) {
      dataArray = rawData.data;
    } else if (rawData.results && Array.isArray(rawData.results)) {
      dataArray = rawData.results;
    } else if (rawData.teams && Array.isArray(rawData.teams)) {
      dataArray = rawData.teams;
    } else {
      return [];
    }

    return dataArray.map((item, index) => {
      // 获取团队ID
      const teamId = item.team_id || item.teamId || item.id;

      // 获取团队名称，优先使用"第X队"格式
      const teamName =
        item.team_name ||
        item.teamName ||
        item.name ||
        item.group_name ||
        (teamId ? getTeamDisplayName(teamId) : null) ||
        '未知团队';

      // 处理团队成员列表
      let members = [];
      if (item.members && Array.isArray(item.members)) {
        members = item.members;
      } else if (item.member_list && Array.isArray(item.member_list)) {
        members = item.member_list;
      } else if (item.users && Array.isArray(item.users)) {
        members = item.users;
      } else if (item.people && Array.isArray(item.people)) {
        members = item.people;
      }

      return {
        id: item.id || item.team_id || item.teamId || index,
        teamId,
        teamName,
        name: teamName,
        points:
          item.points ||
          item.score ||
          item.total_score ||
          item.totalScore ||
          item.totalPoints ||
          0,
        totalPoints:
          item.total_points ||
          item.totalPoints ||
          item.points ||
          item.score ||
          0,
        shareCount:
          item.share_count ||
          item.shareCount ||
          item.contributions ||
          item.shares ||
          0,
        memberCount:
          item.member_count || item.memberCount || members.length || 0,
        members,
        rank: item.rank || item.ranking || index + 1
      };
    });
  };

  // 分享日历状态管理
  const [selectedMonth, setSelectedMonth] = useState(6); // 默认6月
  const [scheduleData, setScheduleData] = useState([]);
  const [scheduleLoading, setScheduleLoading] = useState(false);
  const [scheduleError, setScheduleError] = useState(null);

  // 分享列表数据状态
  const [shareListData, setShareListData] = useState([]);
  const [shareListLoading, setShareListLoading] = useState(false);
  const [shareListError, setShareListError] = useState(null);

  // 重置表单函数
  const resetForm = () => {
    const form = document.querySelector('.simplified-form');
    if (form) form.reset();
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const formData = new FormData(e.target);
    const docLink = formData.get('docLink');

    const data = {
      share_person: formData.get('name'),
      share_topic: formData.get('title'),
      share_summary: formData.get('description'),
      share_link: docLink || ''
    };

    // 验证必填字段
    if (
      !data.share_person ||
      !data.share_topic ||
      !data.share_summary ||
      !docLink ||
      !docLink.trim()
    ) {
      // eslint-disable-next-line no-alert
      alert('请填写所有必填字段');
      return;
    }

    // 验证URL格式
    const urlPattern = /^https?:\/\/.+/;
    if (!urlPattern.test(docLink.trim())) {
      // eslint-disable-next-line no-alert
      alert('请输入正确的URL地址');
      return;
    }
    data.share_link = docLink.trim();

    try {
      // 显示提交中状态
      const submitBtn = e.target.querySelector('.submit-btn');
      submitBtn.textContent = '提交中...';
      submitBtn.disabled = true;

      // 动态导入API服务
      const apiService = (await import('../../utils/api/services')).default;
      await apiService.submitShareRegistration(data);

      // 提交成功
      // eslint-disable-next-line no-alert
      alert('报名提交成功！感谢您的参与。');
      resetForm();
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('报名提交失败:', error);

      // 解析API错误信息
      let errorMessage = '报名提交失败，请稍后重试';

      if (error.message) {
        try {
          // 尝试解析JSON错误响应
          const errorData = JSON.parse(error.message);
          if (errorData.error === '数据验证失败' && errorData.details) {
            const { details } = errorData;
            if (
              details.share_link &&
              details.share_link.includes('Enter a valid URL.')
            ) {
              errorMessage = '请输入正确的URL地址';
            } else {
              // 其他验证错误
              const fieldErrors = Object.entries(details).map(
                ([field, messages]) => {
                  const fieldName =
                    {
                      share_person: '姓名',
                      share_topic: '分享主题',
                      share_summary: '分享简介',
                      share_link: '分享链接'
                    }[field] || field;
                  return `${fieldName}: ${Array.isArray(messages) ? messages.join(', ') : messages}`;
                }
              );
              errorMessage = `数据验证失败：${fieldErrors.join('; ')}`;
            }
          } else if (errorData.error) {
            errorMessage = errorData.error;
          }
        } catch {
          // 如果不是JSON格式，使用原始错误信息
          errorMessage = error.message;
        }
      }

      // eslint-disable-next-line no-alert
      alert(errorMessage);
    } finally {
      // 恢复按钮状态
      const submitBtn = e.target.querySelector('.submit-btn');
      if (submitBtn) {
        submitBtn.textContent = '提交报名';
        submitBtn.disabled = false;
      }
    }
  };

  // 获取分享排班数据
  const fetchScheduleData = async () => {
    setScheduleLoading(true);
    setScheduleError(null);

    try {
      // 动态导入API服务
      const apiService = (await import('../../utils/api/services')).default;
      const data = await apiService.getShareSchedule();
      setScheduleData(data);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('获取分享排班数据失败:', error);
      setScheduleError(error);
    } finally {
      setScheduleLoading(false);
    }
  };

  // 获取分享列表数据
  const fetchShareListData = async () => {
    setShareListLoading(true);
    setShareListError(null);

    try {
      // 动态导入API服务
      const apiService = (await import('../../utils/api/services')).default;
      const data = await apiService.getShareList();
      setShareListData(data);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('获取分享列表数据失败:', error);
      setShareListError(error);
    } finally {
      setShareListLoading(false);
    }
  };

  // 组件加载时获取数据
  useEffect(() => {
    if (activeTab === 'calendar') {
      fetchScheduleData();
    } else if (activeTab === 'home') {
      fetchShareListData();
    }
  }, [activeTab]);

  // 导航项
  const navItems = [
    { id: 'home', label: '全部分享', icon: '🏠' },
    { id: 'register', label: '分享报名', icon: '👤' },
    { id: 'leaderboard', label: '积分排行榜', icon: '🏆' },
    { id: 'calendar', label: '分享日历', icon: '📅' },
    { id: 'points', label: '积分方案', icon: '💎' },
    { id: 'rewards', label: '奖品', icon: '🎁' }
  ];

  return (
    <div className="v0-main-page">
      {/* 顶部导航栏 */}
      <header className="v0-header">
        <div className="v0-header-container">
          <div className="v0-header-content">
            <div className="v0-mobile-menu-button">
              <button
                type="button"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="v0-menu-btn"
              >
                {mobileMenuOpen ? '✕' : '☰'}
              </button>
            </div>
            <h1 className="v0-logo">AI & 多栈技术分享</h1>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="v0-main-content">
        {/* Tab 导航 */}
        {/*
         * Renders the navigation tabs for the main page.
         * Maps through navItems to create interactive tab buttons with icons and labels.
         * Each tab can be selected to change the active view in the application.
         */}
        <div className="v0-tab-nav">
          {navItems.map((item) => (
            <button
              key={item.id}
              type="button"
              onClick={() => setActiveTab(item.id)}
              className={`v0-tab-item ${activeTab === item.id ? 'active' : ''}`}
            >
              <span className="v0-tab-icon">{item.icon}</span>
              <span className="v0-tab-label">{item.label}</span>
            </button>
          ))}
        </div>
        {/* Tab 内容 */}
        <div className="v0-tab-content">
          {/* 首页 Tab */}
          {activeTab === 'home' && (
            <div className="v0-home-content">
              {/* 所有分享列表 */}
              <div className="home-sharing-sections">
                <div className="v0-section all-shares-section">
                  {/* 分享列表 */}
                  <div className="all-shares-list">
                    {(() => {
                      if (shareListLoading) {
                        return (
                          <div className="loading-container">
                            <Spin size="large" />
                            <p>加载分享数据中...</p>
                          </div>
                        );
                      }

                      if (shareListError) {
                        return (
                          <div className="error-message">
                            <p>加载失败: {shareListError.message}</p>
                            <button type="button" onClick={fetchShareListData}>
                              重试
                            </button>
                          </div>
                        );
                      }

                      // 处理服务端分享数据
                      const allShares = [];

                      // 只使用API返回的分享数据
                      if (shareListData && shareListData.length > 0) {
                        shareListData.forEach((item) => {
                          allShares.push({
                            ...item,
                            shareDate: new Date(
                              item.share_date || item.date_range
                            ),
                            speaker:
                              item.share_person || item.presenter || '未知',
                            team: item.team_name || item.team || null,
                            title: item.share_topic || item.topic,
                            description:
                              item.share_summary ||
                              item.description ||
                              '暂无描述',
                            materialUrl: item.share_link || null,
                            status: 'upcoming',
                            // 保留原始API字段以便显示更多信息
                            type: item.type || null,
                            difficulty: item.difficulty || null,
                            location: item.location || null,
                            time: item.time || null
                          });
                        });
                      }

                      // 按日期排序（最新的在前）
                      allShares.sort((a, b) => b.shareDate - a.shareDate);

                      if (allShares.length === 0) {
                        return (
                          <div className="no-data-message">
                            <div className="no-data-icon">📖</div>
                            <p>暂无分享数据</p>
                            <p className="no-data-subtitle">
                              请等待服务端数据加载或联系管理员
                            </p>
                          </div>
                        );
                      }

                      return (
                        <div className="shares-grid">
                          {allShares.map((item, index) => (
                            <div
                              key={item.id || `share-${index}`}
                              className="share-grid-item"
                              style={{
                                minHeight: `${calculateCardHeight(item)}px`
                              }}
                            >
                              {/* 日期标签 */}
                              <div className="grid-date-badge">
                                📅 {item.shareDate.getMonth() + 1}月
                                {item.shareDate.getDate()}日
                              </div>

                              {/* 标题 */}
                              <h3
                                className="grid-title"
                                style={{
                                  WebkitLineClamp: calculateLineClamp(
                                    item.title,
                                    'title'
                                  )
                                }}
                              >
                                {item.title}
                              </h3>

                              {/* 分享人、队伍和分享材料 */}
                              <div className="grid-meta">
                                <div className="grid-speaker">
                                  <span className="meta-icon">👤</span>
                                  <span className="meta-text">
                                    {item.speaker}
                                  </span>
                                </div>
                                {item.team && (
                                  <div className="grid-team">
                                    <span className="meta-text">
                                      {item.team}
                                    </span>
                                    {item.materialUrl && (
                                      <a
                                        href={item.materialUrl}
                                        className="material-link"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        title="查看分享材料"
                                      >
                                        📄 分享材料
                                      </a>
                                    )}
                                  </div>
                                )}
                              </div>

                              {/* 描述 */}
                              <div
                                className="grid-description"
                                style={{
                                  WebkitLineClamp: calculateLineClamp(
                                    item.description,
                                    'description'
                                  )
                                }}
                              >
                                {item.description}
                              </div>
                            </div>
                          ))}
                        </div>
                      );
                    })()}
                  </div>

                  {/* 查看分享日历按钮 */}
                  <div className="calendar-link-section">
                    <button
                      type="button"
                      className="calendar-link-btn"
                      onClick={() => setActiveTab('calendar')}
                    >
                      <div className="calendar-icon">📅</div>
                      <div className="calendar-text">
                        <span className="calendar-title">查看分享日历</span>
                        <span className="calendar-subtitle">
                          查看完整的分享排班安排
                        </span>
                      </div>
                      <div className="calendar-arrow">→</div>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 分享报名 Tab */}
          {activeTab === 'register' && (
            <div className="v0-register-content">
              <div className="v0-section">
                <div className="register-layout">
                  {/* 左侧：报名表单 */}
                  <div className="register-form-section">
                    <form className="simplified-form" onSubmit={handleSubmit}>
                      <div className="form-field">
                        <label className="form-label" htmlFor="name">
                          姓名 *
                        </label>
                        <select
                          id="name"
                          name="name"
                          className="form-input"
                          required
                        >
                          <option value="">请选择您的姓名</option>
                          <option value="李琦">李琦</option>
                          <option value="曾谦">曾谦</option>
                          <option value="陈昌永">陈昌永</option>
                          <option value="陈香玉">陈香玉</option>
                          <option value="陈雅">陈雅</option>
                          <option value="陈亚超">陈亚超</option>
                          <option value="大晓刚">大晓刚</option>
                          <option value="杜娟">杜娟</option>
                          <option value="韩哲">韩哲</option>
                          <option value="侯春艳">侯春艳</option>
                          <option value="黄钱红">黄钱红</option>
                          <option value="贾文超">贾文超</option>
                          <option value="梁家镌">梁家镌</option>
                          <option value="李鹏爽">李鹏爽</option>
                          <option value="李珊珊">李珊珊</option>
                          <option value="刘春锋">刘春锋</option>
                          <option value="刘富承">刘富承</option>
                          <option value="柳祥旭">柳祥旭</option>
                          <option value="刘月">刘月</option>
                          <option value="李颖超">李颖超</option>
                          <option value="卢东肖">卢东肖</option>
                          <option value="马小淋">马小淋</option>
                          <option value="秦玲玲">秦玲玲</option>
                          <option value="宋苏晗">宋苏晗</option>
                          <option value="覃海冰">覃海冰</option>
                          <option value="王宝奇">王宝奇</option>
                          <option value="王晓楠">王晓楠</option>
                          <option value="王莹">王莹</option>
                          <option value="温媛媛">温媛媛</option>
                          <option value="吴桐">吴桐</option>
                          <option value="吴洋">吴洋</option>
                          <option value="项旭">项旭</option>
                          <option value="谢倩娜">谢倩娜</option>
                          <option value="谢文松">谢文松</option>
                          <option value="杨静雯">杨静雯</option>
                          <option value="杨天宇">杨天宇</option>
                          <option value="颜路">颜路</option>
                          <option value="姚献萍">姚献萍</option>
                          <option value="张丹丹">张丹丹</option>
                          <option value="张建宇">张建宇</option>
                          <option value="张莉园">张莉园</option>
                          <option value="张珍心">张珍心</option>
                          <option value="赵宁">赵宁</option>
                          <option value="赵玉">赵玉</option>
                          <option value="赵玉玺">赵玉玺</option>
                          <option value="周希">周希</option>
                          <option value="周雨婷">周雨婷</option>
                          <option value="朱凯莉">朱凯莉</option>
                        </select>
                      </div>

                      <div className="form-field">
                        <label className="form-label" htmlFor="title">
                          分享主题 *
                        </label>
                        <input
                          type="text"
                          id="title"
                          name="title"
                          className="form-input"
                          required
                          placeholder="例如：ChatGPT提升开发效率、Excel数据透视表技巧、Notion协作心得"
                        />
                      </div>

                      <div className="form-field">
                        <label className="form-label" htmlFor="description">
                          分享简介 *
                        </label>
                        <textarea
                          id="description"
                          name="description"
                          className="form-input"
                          required
                          placeholder="请详细描述您的分享内容，建议包含：&#10;• 使用的工具/技术/方法&#10;• 解决的具体问题&#10;• 带来的效果或收益&#10;• 经验教训或注意事项&#10;&#10;示例：使用ChatGPT辅助编写代码和文档，通过合理的提示词设计，将开发效率提升30%。分享将包括提示词技巧、实际案例演示及避坑指南。"
                          rows="8"
                        />
                      </div>

                      <div className="form-field">
                        <label className="form-label" htmlFor="docLink">
                          分享文档链接 *
                        </label>
                        <input
                          type="url"
                          id="docLink"
                          name="docLink"
                          className="form-input"
                          required
                          placeholder="https://docs.google.com/... 或 https://notion.so/..."
                        />
                      </div>

                      <div className="form-actions">
                        <button type="submit" className="submit-btn">
                          提交报名
                        </button>
                        <button
                          type="button"
                          className="reset-btn"
                          onClick={resetForm}
                        >
                          重置表单
                        </button>
                      </div>
                    </form>
                  </div>

                  {/* 右侧：报名指导说明 */}
                  <div className="register-guide-section">
                    <div className="register-guide">
                      <h3 className="guide-title">📝 分享报名指南</h3>
                      <div className="guide-content">
                        <div className="guide-tips">
                          <div className="tip-item">
                            <span className="tip-icon">💡</span>
                            <span className="tip-text">
                              <strong>主题建议：</strong>
                              工具使用技巧、工作流程优化、技术解决方案、经验总结等
                            </span>
                          </div>
                          <div className="tip-item">
                            <span className="tip-icon">⭐</span>
                            <span className="tip-text">
                              <strong>内容要求：</strong>
                              实用性强、有具体案例、能给同事带来启发或帮助
                            </span>
                          </div>
                          <div className="tip-item">
                            <span className="tip-icon">🎯</span>
                            <span className="tip-text">
                              <strong>积分奖励：</strong>
                              提前报名+15分，完成分享+10分，优质内容还有额外加分
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 积分排行榜 Tab */}
          {activeTab === 'leaderboard' && (
            <div className="v0-leaderboard-content">
              <div className="v0-section">
                {/* 并排展示个人排行和团队排行 */}
                <div className="v0-leaderboard-grid">
                  {/* 个人排行榜 */}
                  <div className="v0-leaderboard-column">
                    <div className="leaderboard-header">
                      <h3 className="v0-leaderboard-subtitle">个人排行</h3>
                      <button
                        type="button"
                        className="refresh-btn"
                        onClick={refreshPersonal}
                        disabled={personalLoading}
                      >
                        {personalLoading ? (
                          <>
                            <Spin size="small" />
                            <span className="refresh-text">刷新中</span>
                          </>
                        ) : (
                          <>
                            <RefreshIcon />
                            <span className="refresh-text">刷新</span>
                          </>
                        )}
                      </button>
                    </div>

                    {personalError ? (
                      <div className="error-message">
                        <p>加载失败: {personalError.message}</p>
                        <button type="button" onClick={refreshPersonal}>
                          重试
                        </button>
                      </div>
                    ) : (
                      <div className="v0-leaderboard-list">
                        {(() => {
                          const processedData =
                            processPersonalLeaderboardData(personalLeaderboard);

                          if (processedData.length === 0) {
                            return (
                              <div className="no-data">
                                <p>暂无数据</p>
                              </div>
                            );
                          }

                          return processedData.map((item) => (
                            <div
                              key={item.userId || item.id}
                              className={`v0-leaderboard-item ${item.rank <= 3 ? `rank-${item.rank}-item` : ''}`}
                            >
                              <div
                                className={`v0-rank-badge rank-${item.rank <= 3 ? item.rank : 'other'}`}
                              >
                                {item.rank}
                              </div>
                              <div className="v0-user-info">
                                <div className="v0-user-name">{item.name}</div>
                                <div className="v0-user-team">
                                  {item.team || '暂无团队信息'}
                                </div>
                              </div>
                              <div className="v0-user-stats">
                                <div className="v0-points">
                                  {item.points} 分
                                </div>
                                <div className="v0-contributions">
                                  分享 {item.shareCount} 次
                                  {item.qualityCount > 0 && (
                                    <span className="v0-quality-badge">
                                      🏆 {item.qualityCount}次优质
                                    </span>
                                  )}
                                </div>
                              </div>
                            </div>
                          ));
                        })()}
                      </div>
                    )}
                  </div>

                  {/* 团队排行榜 */}
                  <div className="v0-leaderboard-column">
                    <div className="leaderboard-header">
                      <h3 className="v0-leaderboard-subtitle">团队排行</h3>
                      <button
                        type="button"
                        className="refresh-btn"
                        onClick={refreshTeam}
                        disabled={teamLoading}
                      >
                        {teamLoading ? (
                          <>
                            <Spin size="small" />
                            <span className="refresh-text">刷新中</span>
                          </>
                        ) : (
                          <>
                            <RefreshIcon />
                            <span className="refresh-text">刷新</span>
                          </>
                        )}
                      </button>
                    </div>

                    {teamError ? (
                      <div className="error-message">
                        <p>加载失败: {teamError.message}</p>
                        <button type="button" onClick={refreshTeam}>
                          重试
                        </button>
                      </div>
                    ) : (
                      <div className="v0-leaderboard-list">
                        {(() => {
                          const processedData =
                            processTeamLeaderboardData(teamLeaderboard);

                          if (processedData.length === 0) {
                            return (
                              <div className="no-data">
                                <p>暂无数据</p>
                              </div>
                            );
                          }

                          return processedData.map((item) => (
                            <div
                              key={item.teamId || item.id}
                              className={`v0-leaderboard-item ${item.rank <= 3 ? `rank-${item.rank}-item` : ''}`}
                            >
                              <div
                                className={`v0-rank-badge rank-${item.rank <= 3 ? item.rank : 'other'}`}
                              >
                                {item.rank}
                              </div>
                              <div className="v0-user-info">
                                <div className="v0-user-name">
                                  {item.teamName}
                                </div>
                                <div className="v0-user-team">
                                  {item.members && item.members.length > 0 ? (
                                    <div className="team-members">
                                      {item.members.map(
                                        (member, memberIndex) => {
                                          const memberName =
                                            member.name ||
                                            member.member_name ||
                                            member.user_name ||
                                            '未知成员';
                                          const isCaptain =
                                            member.is_captain ||
                                            member.isCaptain ||
                                            false;

                                          return (
                                            <span
                                              key={
                                                member.id ||
                                                member.member_id ||
                                                memberIndex
                                              }
                                              className="member-name"
                                            >
                                              {memberName}
                                              {isCaptain && (
                                                <span className="captain-badge">
                                                  👑
                                                </span>
                                              )}
                                              {memberIndex <
                                                item.members.length - 1 && '、'}
                                            </span>
                                          );
                                        }
                                      )}
                                    </div>
                                  ) : (
                                    <span>
                                      暂无成员信息{' '}
                                      {item.memberCount > 0 &&
                                        `(${item.memberCount}人)`}
                                    </span>
                                  )}
                                </div>
                              </div>
                              <div className="v0-user-stats">
                                <div className="v0-points">
                                  {item.points} 分
                                </div>
                                <div className="v0-contributions">
                                  分享 {item.shareCount} 次
                                </div>
                              </div>
                            </div>
                          ));
                        })()}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 分享日历 Tab */}
          {activeTab === 'calendar' && (
            <div className="v0-calendar-content">
              <div className="v0-section">
                {/* 月份选择器 */}
                <div className="month-selector">
                  {[5, 6, 7, 8, 9, 10].map((month) => (
                    <button
                      key={month}
                      type="button"
                      className={`month-btn ${selectedMonth === month ? 'active' : ''}`}
                      onClick={() => setSelectedMonth(month)}
                    >
                      {month}月
                    </button>
                  ))}
                </div>

                {/* 日历网格 */}
                <div className="schedule-calendar">
                  {/* 星期标题 */}
                  <div className="weekdays-header">
                    <div className="weekday">一</div>
                    <div className="weekday">二</div>
                    <div className="weekday">三</div>
                    <div className="weekday">四</div>
                    <div className="weekday">五</div>
                    <div className="weekday">六</div>
                    <div className="weekday">日</div>
                  </div>

                  {/* 日历主体 */}
                  <div className="calendar-body">
                    {(() => {
                      if (scheduleLoading) {
                        return (
                          <div className="loading-container">
                            <Spin size="large" />
                            <p>加载分享排班数据中...</p>
                          </div>
                        );
                      }

                      if (scheduleError) {
                        return (
                          <div className="error-message">
                            <p>加载失败: {scheduleError.message}</p>
                            <button type="button" onClick={fetchScheduleData}>
                              重试
                            </button>
                          </div>
                        );
                      }

                      // 生成真实的系统日历
                      const currentYear = new Date().getFullYear();
                      const weeks = generateRealCalendar(
                        currentYear,
                        selectedMonth,
                        scheduleData
                      );

                      return (
                        <div className="calendar-container">
                          {/* 日历周行 */}
                          {weeks.map((week, weekIndex) => {
                            // 生成基于第一天日期的唯一key
                            const firstDayOfWeek = week.days[0];
                            const weekKey = `week-${currentYear}-${selectedMonth}-${firstDayOfWeek.date}-${weekIndex}`;

                            return (
                              <div key={weekKey} className="calendar-week">
                                {/* 日期格子 */}
                                <div className="week-days">
                                  {week.days.map((day) => (
                                    <div
                                      key={`day-${day.fullDate.getTime()}`}
                                      className={`date-cell ${
                                        !day.isCurrentMonth ? 'other-month' : ''
                                      } ${day.hasShare ? 'has-event' : ''}`}
                                    >
                                      <div className="date-number">
                                        {day.date}
                                      </div>
                                    </div>
                                  ))}
                                </div>

                                {/* 事件条 - 跨越整周 */}
                                {week.hasEvents && (
                                  <div className="week-event-bar">
                                    {week.eventTeams}
                                  </div>
                                )}
                              </div>
                            );
                          })}
                        </div>
                      );
                    })()}
                  </div>

                  {/* 说明文字 */}
                  <div className="calendar-notice">
                    <p>📝 说明：本周分享排班的队伍需要最少出一个分享</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 积分方案 Tab */}
          {activeTab === 'points' && (
            <div className="v0-points-content">
              <div className="v0-section">
                <div className="points-rules">
                  {/* 积分规则总览 */}
                  <div className="rule-overview">
                    <h2 className="overview-title">💡 积分获取策略</h2>
                    <p className="overview-desc">
                      主动报名、转换高质量文章、内部票选优秀案例均可以暴拉积分
                    </p>
                  </div>

                  {/* 1. 分享参与类（基础分） */}
                  <div className="rule-card">
                    <h3 className="rule-title">📝 分享参与类（基础分）</h3>
                    <div className="rule-content">
                      <div className="rule-table">
                        <div className="table-header">
                          <div className="col-behavior">行为</div>
                          <div className="col-personal">个人积分</div>
                          <div className="col-team">团队积分</div>
                          <div className="col-desc">说明</div>
                        </div>
                        <div className="table-row">
                          <div className="col-behavior">
                            提前超过1周以上报名分享
                          </div>
                          <div className="col-personal">+15</div>
                          <div className="col-team">+10</div>
                          <div className="col-desc">
                            需提前一周报名（活动第一周主动报名的算提前一周）
                          </div>
                        </div>
                        <div className="table-row">
                          <div className="col-behavior">分享一次</div>
                          <div className="col-personal">+10</div>
                          <div className="col-team">+10</div>
                          <div className="col-desc">符合内容标准即可</div>
                        </div>
                      </div>
                      <div className="rule-example">
                        <strong>示例：</strong>
                        提前一周以上+15分，分享一次基础分10分；本次分享共个人+25分，团队+20分
                      </div>
                    </div>
                  </div>

                  {/* 2. 案例质量类（加分项） */}
                  <div className="rule-card">
                    <h3 className="rule-title">⭐ 案例质量类（加分项）</h3>
                    <div className="rule-content">
                      <div className="rule-table">
                        <div className="table-header">
                          <div className="col-behavior">行为</div>
                          <div className="col-personal">个人积分</div>
                          <div className="col-team">团队积分</div>
                          <div className="col-desc">说明</div>
                        </div>
                        <div className="table-row">
                          <div className="col-behavior">
                            案例转化为公司内部公众号文章
                          </div>
                          <div className="col-personal">+20</div>
                          <div className="col-team">+10</div>
                          <div className="col-desc">正式发表</div>
                        </div>
                        <div className="table-row">
                          <div className="col-behavior">
                            案例申请为专利（提交申请即可）
                          </div>
                          <div className="col-personal">+30</div>
                          <div className="col-team">+20</div>
                          <div className="col-desc">申请通过</div>
                        </div>
                        <div className="table-row">
                          <div className="col-behavior">
                            案例被评为「本场优秀案例Top3」
                          </div>
                          <div className="col-personal">+15</div>
                          <div className="col-team">+10</div>
                          <div className="col-desc">每场分享大众评选</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 3. 团队贡献类（团队拉动分） */}
                  <div className="rule-card">
                    <h3 className="rule-title">🚀 团队贡献类（团队拉动分）</h3>
                    <div className="rule-content">
                      <div className="rule-table team-table">
                        <div className="table-header">
                          <div className="col-behavior">行为</div>
                          <div className="col-team-only">团队积分</div>
                          <div className="col-desc">说明</div>
                        </div>
                        <div className="table-row">
                          <div className="col-behavior">
                            组内月度分享数量≥2个
                          </div>
                          <div className="col-team-only">+20</div>
                          <div className="col-desc">按月结算</div>
                        </div>
                        <div className="table-row">
                          <div className="col-behavior">
                            组内月度分享数量≥4个（翻倍奖励）
                          </div>
                          <div className="col-team-only">+40</div>
                          <div className="col-desc">按月结算</div>
                        </div>
                        <div className="table-row">
                          <div className="col-behavior">组内优秀案例≥2</div>
                          <div className="col-team-only">+20</div>
                          <div className="col-desc">按月结算</div>
                        </div>
                      </div>
                      <div className="rule-note">每个月最后周进行自动结算</div>
                    </div>
                  </div>

                  {/* 4. 扣分规则（负激励） */}
                  <div className="rule-card penalty-card">
                    <h3 className="rule-title">⚠️ 扣分规则（负激励）</h3>
                    <div className="rule-content">
                      <div className="rule-table">
                        <div className="table-header">
                          <div className="col-behavior">行为</div>
                          <div className="col-personal">个人扣分</div>
                          <div className="col-team">团队扣分</div>
                          <div className="col-desc">说明</div>
                        </div>
                        <div className="table-row">
                          <div className="col-behavior">
                            未完成每月最少分享要求（组）
                          </div>
                          <div className="col-personal">-5</div>
                          <div className="col-team">-5</div>
                          <div className="col-desc">每人扣除，组整体扣除</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 实用案例 */}
                  <div className="rule-card example-card">
                    <h3 className="rule-title">💡 实用案例</h3>
                    <div className="rule-content">
                      <div className="case-example">
                        <h4>Case1: 我有个例子我下个月再分享吧？</h4>
                        <p>
                          如果主动且提前报名可以个人+15分，团队+10分，即使下个月不分享个人和团队才各扣5分，
                          <strong>所以一定要提前主动分享，赚的更多</strong>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 奖品 Tab */}
          {activeTab === 'rewards' && (
            <div className="v0-rewards-content">
              <div className="v0-section">
                <div className="rewards-grid">
                  <div className="reward-card">
                    <div className="reward-image">🎧</div>
                    <h3 className="reward-title">蓝牙耳机</h3>
                    <div className="reward-points">500积分</div>
                    <div className="reward-desc">
                      高品质无线蓝牙耳机，降噪功能
                    </div>
                    <button type="button" className="reward-btn">
                      立即兑换
                    </button>
                  </div>

                  <div className="reward-card">
                    <div className="reward-image">📱</div>
                    <h3 className="reward-title">手机支架</h3>
                    <div className="reward-points">200积分</div>
                    <div className="reward-desc">
                      多角度调节，适用于各种手机
                    </div>
                    <button type="button" className="reward-btn">
                      立即兑换
                    </button>
                  </div>

                  <div className="reward-card">
                    <div className="reward-image">☕</div>
                    <h3 className="reward-title">咖啡券</h3>
                    <div className="reward-points">100积分</div>
                    <div className="reward-desc">
                      星巴克咖啡券，任选中杯饮品
                    </div>
                    <button type="button" className="reward-btn">
                      立即兑换
                    </button>
                  </div>

                  <div className="reward-card">
                    <div className="reward-image">📚</div>
                    <h3 className="reward-title">技术书籍</h3>
                    <div className="reward-points">300积分</div>
                    <div className="reward-desc">
                      最新技术书籍，提升专业技能
                    </div>
                    <button type="button" className="reward-btn">
                      立即兑换
                    </button>
                  </div>

                  <div className="reward-card">
                    <div className="reward-image">🎮</div>
                    <h3 className="reward-title">游戏手柄</h3>
                    <div className="reward-points">800积分</div>
                    <div className="reward-desc">无线游戏手柄，支持多平台</div>
                    <button type="button" className="reward-btn">
                      立即兑换
                    </button>
                  </div>

                  <div className="reward-card">
                    <div className="reward-image">🎁</div>
                    <h3 className="reward-title">神秘礼品</h3>
                    <div className="reward-points">1000积分</div>
                    <div className="reward-desc">
                      限量版神秘礼品，惊喜等你发现
                    </div>
                    <button type="button" className="reward-btn">
                      立即兑换
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
