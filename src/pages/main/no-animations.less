/* 禁用所有动画效果 - 提升切换速度 */

/* 全局禁用动画和过渡效果 */
*,
*::before,
*::after {
  animation-duration: 0s !important;
  animation-delay: 0s !important;
  transition-duration: 0s !important;
  transition-delay: 0s !important;
}

/* 禁用背景动画 */
.v0-main-page::before,
.v0-main-page::after {
  animation: none !important;
}

/* 禁用页面加载动画 */
.v0-main-page {
  animation: none !important;
}

/* 禁用头部动画 */
.v0-header {
  animation: none !important;
  transition: none !important;
}

/* 禁用Logo动画 */
.v0-logo {
  animation: none !important;
  transition: none !important;
}

.v0-logo:hover {
  transform: none !important;
}

/* 禁用主内容动画 */
.v0-main-content {
  animation: none !important;
}

/* 禁用Tab导航动画 */
.v0-tab-nav {
  animation: none !important;
}

.v0-tab-item {
  transition: none !important;
}

.v0-tab-item::before {
  transition: none !important;
}

.v0-tab-item:hover {
  transform: none !important;
}

.v0-tab-item.active {
  transform: none !important;
}

/* 禁用区块动画 */
.v0-section {
  animation: none !important;
  transition: none !important;
}

.v0-section:hover {
  transform: none !important;
}

.v0-section-title::after {
  transition: none !important;
}

/* 禁用卡片动画 */
.v0-sharing-card,
.current-sharing-card {
  animation: none !important;
  transition: none !important;
}

.v0-sharing-card::before {
  transition: none !important;
}

.v0-sharing-card:hover {
  transform: none !important;
}

/* 禁用排行榜动画 */
.v0-leaderboard-column {
  transition: none !important;
}

.v0-leaderboard-column::before {
  transition: none !important;
}

.v0-leaderboard-column:hover {
  transform: none !important;
}

.v0-leaderboard-item {
  transition: none !important;
}

.v0-leaderboard-item::before {
  transition: none !important;
}

.v0-leaderboard-item:hover {
  transform: none !important;
}

.v0-rank-badge {
  transition: none !important;
}

.v0-rank-badge::before {
  transition: none !important;
}

.v0-user-name {
  transition: none !important;
}

.v0-points {
  transition: none !important;
}

.v0-points:hover {
  transform: none !important;
}

/* 禁用按钮动画 */
.v0-register-btn,
.submit-btn,
.reset-btn,
.reward-btn,
.calendar-link-btn {
  transition: none !important;
}

.v0-register-btn:hover,
.submit-btn:hover,
.calendar-link-btn:hover {
  transform: none !important;
}

.v0-register-btn:active,
.submit-btn:active {
  transform: none !important;
}

/* 禁用表单动画 */
.form-input {
  transition: none !important;
}

/* 禁用奖品卡片动画 */
.reward-card {
  transition: none !important;
}

.reward-card:hover {
  transform: none !important;
}

/* 禁用历史项目动画 */
.upcoming-item,
.history-item {
  transition: none !important;
}

.upcoming-item:hover,
.history-item:hover {
  transform: none !important;
}

/* 禁用日历链接动画 */
.calendar-link-btn::before {
  transition: none !important;
}

.calendar-arrow {
  transition: none !important;
}

.calendar-link-btn:hover .calendar-arrow {
  transform: none !important;
}

/* 禁用材料链接动画 */
.material-link,
.v0-material-link {
  transition: none !important;
}

.material-link:hover,
.v0-material-link:hover {
  transform: none !important;
}

/* 禁用导航卡片动画 */
.v0-nav-card {
  animation: none !important;
  transition: none !important;
}

.v0-nav-card::before {
  transition: none !important;
}

.v0-nav-card:hover {
  transform: none !important;
}

.v0-nav-card-icon {
  transition: none !important;
}

.v0-nav-card-icon::before {
  transition: none !important;
}

.v0-nav-card:hover .v0-nav-card-icon {
  transform: none !important;
}

.v0-nav-card:hover .v0-nav-card-icon::before {
  transform: none !important;
}

.v0-nav-card-arrow {
  transition: none !important;
}

.v0-nav-card:hover .v0-nav-card-arrow {
  transform: none !important;
  animation: none !important;
}

/* 禁用徽章动画 */
.v0-badge {
  transition: none !important;
}

.v0-badge:hover {
  transform: none !important;
}

/* 禁用刷新按钮动画 */
.refresh-btn {
  transition: none !important;
}

/* 禁用月份选择器动画 */
.month-btn {
  transition: none !important;
}

/* 禁用规则卡片动画 */
.rule-card {
  transition: none !important;
}

.rule-card:hover {
  transform: none !important;
}

/* 禁用所有hover效果的transform */
*:hover {
  transform: none !important;
}

/* 禁用所有keyframes动画 */
@keyframes backgroundPulse { to { opacity: 1; } }
@keyframes backgroundShift { to { transform: none; } }
@keyframes pageSlideIn { to { opacity: 1; transform: none; } }
@keyframes headerSlideDown { to { opacity: 1; transform: none; } }
@keyframes logoFadeIn { to { opacity: 1; transform: none; } }
@keyframes contentFadeIn { to { opacity: 1; transform: none; } }
@keyframes tabSlideIn { to { opacity: 1; transform: none; } }
@keyframes cardSlideIn { to { opacity: 1; transform: none; } }
@keyframes cardFadeIn { to { opacity: 1; transform: none; } }
@keyframes sectionFadeIn { to { opacity: 1; transform: none; } }
@keyframes arrowBounce { to { transform: none; } }
