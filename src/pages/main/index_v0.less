/* 现代化动画主页样式 - 明亮背景 */
.v0-main-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #fefefe 50%, #fdfdfd 100%);
  background-attachment: fixed;
  position: relative;
  overflow-x: hidden;
}

.v0-main-page::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 25% 75%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 25%, rgba(16, 185, 129, 0.02) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(139, 92, 246, 0.015) 0%, transparent 50%);
  pointer-events: none;
  z-index: 1;
  animation: backgroundPulse 12s ease-in-out infinite;
}

.v0-main-page::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(45deg, transparent 40%, rgba(59, 130, 246, 0.008) 50%, transparent 60%),
    linear-gradient(-45deg, transparent 40%, rgba(16, 185, 129, 0.006) 50%, transparent 60%);
  pointer-events: none;
  z-index: 1;
  animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes backgroundShift {
  0%, 100% { transform: translateX(0) translateY(0); }
  25% { transform: translateX(10px) translateY(-5px); }
  50% { transform: translateX(-5px) translateY(10px); }
  75% { transform: translateX(-10px) translateY(-10px); }
}

/* 页面加载动画 */
.v0-main-page {
  animation: pageSlideIn 0.6s ease-out;
}

@keyframes pageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 顶部导航栏 - 动画增强 */
.v0-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(12px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid #d1d9e0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 64px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: headerSlideDown 0.5s ease-out;
}

@keyframes headerSlideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.v0-header:hover {
  box-shadow: 0 2px 8px rgba(9, 105, 218, 0.15);
}

.v0-header-container {
  max-width: 1000px;
  margin: 0 auto;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1rem;
}

.v0-header-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
}

.v0-mobile-menu-button {
  display: none;
}

.v0-menu-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.v0-menu-btn:hover {
  background-color: #f3f4f6;
}

.v0-logo {
  font-size: 1.75rem;
  font-weight: 600;
  margin: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.02em;
  cursor: pointer;
  transition: all 0.3s ease;
  animation: logoFadeIn 0.8s ease-out 0.2s both;
}

@keyframes logoFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.v0-logo:hover {
  transform: scale(1.05);
  filter: brightness(1.1);
}

/* 主要内容 - 紧凑布局 */
.v0-main-content {
  max-width: 1000px;
  margin: 0 auto;
  padding: 76px 1rem 1rem;
  position: relative;
  z-index: 2;
  animation: contentFadeIn 0.8s ease-out 0.4s both;
}

@keyframes contentFadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Tab 导航 - 动画增强 */
.v0-tab-nav {
  display: flex;
  background: #ffffff;
  border-radius: 12px;
  padding: 0.375rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #d1d9e0;
  overflow-x: auto;
  gap: 0.25rem;
  animation: tabSlideIn 0.6s ease-out 0.6s both;
}

@keyframes tabSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.v0-tab-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border: none;
  background: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  font-size: 0.9375rem;
  font-weight: 500;
  color: #656d76;
  flex: 1;
  min-width: 0;
  position: relative;
  overflow: hidden;
}

.v0-tab-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(9, 105, 218, 0.1), transparent);
  transition: left 0.5s ease;
}

.v0-tab-item:hover::before {
  left: 100%;
}

.v0-tab-item:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  color: #475569;
  transform: translateY(-1px);
}

.v0-tab-item.active {
  background: linear-gradient(135deg, #e0e7ff 0%, #f3f4f6 100%);
  color: #4f46e5;
  box-shadow:
    0 2px 8px rgba(79, 70, 229, 0.15),
    0 1px 3px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.v0-tab-item.active::before {
  display: none;
}

.v0-tab-icon {
  font-size: 1rem;
}

.v0-tab-label {
  font-weight: 500;
}

/* Tab 内容 */
.v0-tab-content {
  min-height: 400px;
}

/* 首页内容 */
.v0-home-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.v0-nav-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(9, 105, 218, 0.05), transparent);
  transition: left 0.6s ease;
}

.v0-nav-card:hover::before {
  left: 100%;
}

.v0-nav-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.25);
  border-color: #667eea;
}

.v0-nav-card-icon {
  font-size: 1.875rem;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #e0e7ff 0%, #f3f4f6 50%, #fef3c7 100%);
  border-radius: 16px;
  color: #4f46e5;
  box-shadow:
    0 4px 15px rgba(79, 70, 229, 0.15),
    0 1px 3px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1;
  overflow: hidden;
  border: 1px solid rgba(79, 70, 229, 0.1);
}

.v0-nav-card-icon::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
  border-radius: 16px;
  opacity: 0;
  transition: all 0.6s ease;
  transform: rotate(-45deg);
}

.v0-nav-card:hover .v0-nav-card-icon {
  transform: scale(1.1) rotate(3deg);
  box-shadow:
    0 8px 25px rgba(102, 126, 234, 0.4),
    0 2px 8px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.v0-nav-card:hover .v0-nav-card-icon::before {
  opacity: 1;
  transform: rotate(-45deg) translate(50%, 50%);
}

.v0-nav-card-content {
  flex: 1;
}

.v0-nav-card-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #24292f;
  margin: 0 0 0.25rem 0;
}

.v0-nav-card-content p {
  font-size: 1rem;
  color: #656d76;
  margin: 0;
  line-height: 1.5;
}

.v0-nav-card-arrow {
  font-size: 1.25rem;
  color: #8c959f;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1;
}

.v0-nav-card:hover .v0-nav-card-arrow {
  color: #667eea;
  transform: translateX(8px) scale(1.2);
  animation: arrowBounce 0.6s ease-in-out;
}

@keyframes arrowBounce {
  0%, 100% { transform: translateX(8px) scale(1.2); }
  50% { transform: translateX(12px) scale(1.3); }
}

/* 通用区块 - 紧凑动画 */
.v0-section {
  background: #ffffff;
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid #d1d9e0;
  margin-bottom: 0.75rem;
  transition: all 0.3s ease;
  animation: sectionFadeIn 0.6s ease-out both;
}

.v0-section:nth-of-type(1) { animation-delay: 1.2s; }
.v0-section:nth-of-type(2) { animation-delay: 1.3s; }
.v0-section:nth-of-type(3) { animation-delay: 1.4s; }

@keyframes sectionFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.v0-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(9, 105, 218, 0.1);
}

.v0-section-title {
  font-size: 1.125rem;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 0.75rem 0;
  position: relative;
}

.v0-section-title::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 0;
  width: 0;
  height: 3px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
  transition: width 0.6s ease;
}

.v0-section:hover .v0-section-title::after {
  width: 60px;
}

/* 会话信息 */
.v0-session-info {
  margin-bottom: 1.5rem;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  border: 1px solid #bae6fd;
  animation: contentFadeIn 1.0s ease-out both;
}

.v0-session-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  align-items: center;
}

.v0-session-time,
.v0-session-location {
  display: flex;
  align-items: center;
  font-size: 0.9375rem;
  font-weight: 500;
  color: #0369a1;
}

/* 分享网格 - 紧凑动画 */
.v0-sharing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 0.75rem;
}

.v0-sharing-card {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.08),
    0 1px 3px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  animation: cardFadeIn 0.5s ease-out both;
}

.v0-sharing-card:nth-child(1) { animation-delay: 0.1s; }
.v0-sharing-card:nth-child(2) { animation-delay: 0.2s; }
.v0-sharing-card:nth-child(3) { animation-delay: 0.3s; }

@keyframes cardFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.v0-sharing-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(9, 105, 218, 0.03), transparent);
  transition: left 0.5s ease;
}

.v0-sharing-card:hover::before {
  left: 100%;
}

.v0-sharing-card:hover {
  border-color: #667eea;
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.2);
}

.v0-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
  gap: 0.75rem;
}

.v0-card-time {
  font-size: 0.9375rem;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  z-index: 1;
}

.v0-card-badges {
  display: flex;
  gap: 0.375rem;
  flex-wrap: wrap;
}

.v0-badge {
  padding: 0.25rem 0.625rem;
  border-radius: 6px;
  font-size: 0.8125rem;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.v0-badge:hover {
  transform: scale(1.05);
}

.v0-card-title {
  font-size: 1.25rem;
  font-weight: 600;
  background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 0.75rem 0;
  line-height: 1.4;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.v0-sharing-card:hover .v0-card-title {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.v0-card-description {
  font-size: 1rem;
  color: #64748b;
  line-height: 1.6;
  margin: 0 0 1rem 0;
  position: relative;
  z-index: 1;
  font-weight: 400;
}

.v0-card-info {
  margin: 1rem 0;
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.v0-info-item {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
}

.v0-info-label {
  font-weight: 500;
  color: #475569;
  margin-right: 0.5rem;
}

.v0-material-link {
  color: #4f46e5;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  border-radius: 4px;
  padding: 2px 6px;
}

.v0-material-link:hover {
  color: #3730a3;
  background: rgba(79, 70, 229, 0.1);
  text-decoration: underline;
}

.v0-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  padding-top: 0.75rem;
  border-top: 1px solid #e5e7eb;
  font-size: 0.875rem;
  color: #656d76;
  position: relative;
  z-index: 1;
}

.v0-card-speaker {
  font-weight: 500;
  color: #24292f;
}

.v0-material-link-small {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
  text-decoration: none;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(59, 130, 246, 0.3);

  &:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(59, 130, 246, 0.4);
    color: #ffffff;
    text-decoration: none;
  }

  &:active {
    transform: translateY(0);
  }
}

/* 报名页面 */
.v0-register-notice {
  text-align: center;
  padding: 2rem;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;

  p {
    color: #6b7280;
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
  }
}

.v0-register-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.v0-register-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.v0-register-btn:active {
  transform: translateY(0);
}

/* 排行榜 - 并排展示 - 优化版 */
.v0-leaderboard-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 1.5rem;
}

.v0-leaderboard-column {
  background: #ffffff;
  border-radius: 20px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.08),
    0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.v0-leaderboard-column::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(29, 78, 216, 0.01) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.v0-leaderboard-column:hover {
  box-shadow:
    0 8px 30px rgba(59, 130, 246, 0.15),
    0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-4px) scale(1.02);
  border-color: #3b82f6;
}

.v0-leaderboard-column:hover::before {
  opacity: 1;
}

.v0-leaderboard-subtitle {
  margin: 0;
  padding: 2rem 1.5rem 1.5rem;
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border-bottom: none;
  position: relative;
  text-align: center;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  overflow: hidden;
}

.v0-leaderboard-subtitle::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: rotate(-45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

.v0-leaderboard-column:hover .v0-leaderboard-subtitle::before {
  opacity: 1;
  transform: rotate(-45deg) translate(50%, 50%);
}

.v0-leaderboard-subtitle::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
  border-radius: 2px;
}

.v0-leaderboard-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1.5rem;
  background: linear-gradient(to bottom, #f8fafc 0%, #ffffff 100%);
}

.v0-leaderboard-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem 1rem;
  background: #ffffff;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.v0-leaderboard-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -10px;
  right: -10px;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.06) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 12px;
}

.v0-leaderboard-item:hover {
  border-color: #667eea;
  box-shadow:
    0 8px 25px rgba(102, 126, 234, 0.15),
    0 3px 10px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px) scale(1.01);
}

.v0-leaderboard-item:hover::before {
  opacity: 1;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.12) 0%, rgba(118, 75, 162, 0.08) 100%);
}

/* 移除背景条效果 */
.v0-leaderboard-item::after {
  display: none;
}

.v0-rank-badge {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-weight: 700;
  font-size: 1.1rem;
  color: #ffffff;
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.2),
    0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 2;
}

.v0-rank-badge::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.3), transparent);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.v0-leaderboard-item:hover .v0-rank-badge::before {
  opacity: 1;
}

.v0-rank-badge.rank-1 {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  box-shadow:
    0 4px 20px rgba(251, 191, 36, 0.4),
    0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 第一名特殊背景条 */
.v0-leaderboard-item.rank-1-item::after {
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(251, 191, 36, 0.4) 10%,
    rgba(251, 191, 36, 0.8) 50%,
    rgba(245, 158, 11, 0.4) 90%,
    transparent 100%);
}

.v0-rank-badge.rank-2 {
  background: linear-gradient(135deg, #e5e7eb 0%, #9ca3af 100%);
  box-shadow:
    0 4px 20px rgba(156, 163, 175, 0.4),
    0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 第二名特殊背景条 */
.v0-leaderboard-item.rank-2-item::after {
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(156, 163, 175, 0.4) 10%,
    rgba(156, 163, 175, 0.7) 50%,
    rgba(107, 114, 128, 0.4) 90%,
    transparent 100%);
}

.v0-rank-badge.rank-3 {
  background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
  box-shadow:
    0 4px 20px rgba(249, 115, 22, 0.4),
    0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 第三名特殊背景条 */
.v0-leaderboard-item.rank-3-item::after {
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(249, 115, 22, 0.4) 10%,
    rgba(249, 115, 22, 0.7) 50%,
    rgba(234, 88, 12, 0.4) 90%,
    transparent 100%);
}

.v0-rank-badge.rank-other {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow:
    0 4px 20px rgba(102, 126, 234, 0.4),
    0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 移除不必要的动画 */

.v0-user-info {
  flex: 1;
  position: relative;
  z-index: 2;
}

.v0-user-name {
  font-size: 1.125rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.375rem;
  transition: color 0.3s ease;
}

.v0-leaderboard-item:hover .v0-user-name {
  color: #667eea;
}

.v0-user-team {
  font-size: 0.9375rem;
  color: #64748b;
  font-weight: 500;
  padding: 0.25rem 0.75rem;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-radius: 12px;
  display: inline-block;
  border: 1px solid #e2e8f0;
}

.v0-user-stats {
  text-align: right;
  position: relative;
  z-index: 2;
}

.v0-points {
  font-size: 1.25rem;
  font-weight: 700;
  background: linear-gradient(135deg, #059669 0%, #10b981 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.375rem;
  transition: all 0.3s ease;
}

.v0-leaderboard-item:hover .v0-points {
  transform: scale(1.05);
}

.v0-contributions {
  font-size: 0.9375rem;
  color: #64748b;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: flex-end;
  font-weight: 500;
}

.v0-quality-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.125rem 0.375rem;
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: #ffffff;
  font-size: 0.625rem;
  font-weight: 600;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(245, 158, 11, 0.3);
  white-space: nowrap;
}

/* 徽章颜色 - 美化版 */
.bg-blue-100 {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
}
.text-blue-800 { color: #1e40af; }

.bg-green-100 {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border: 1px solid rgba(34, 197, 94, 0.2);
}
.text-green-800 { color: #166534; }

.bg-yellow-100 {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 1px solid rgba(245, 158, 11, 0.2);
}
.text-yellow-800 { color: #92400e; }

.bg-purple-100 {
  background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
  border: 1px solid rgba(139, 92, 246, 0.2);
}
.text-purple-800 { color: #5b21b6; }

.bg-pink-100 {
  background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%);
  border: 1px solid rgba(236, 72, 153, 0.2);
}
.text-pink-800 { color: #be185d; }

.bg-red-100 {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  border: 1px solid rgba(239, 68, 68, 0.2);
}
.text-red-800 { color: #991b1b; }

.bg-gray-100 {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border: 1px solid rgba(107, 114, 128, 0.2);
}
.text-gray-800 { color: #1f2937; }

/* 响应式设计 - 紧凑优化 */
@media (max-width: 768px) {
  .v0-mobile-menu-button {
    display: block;
  }

  .v0-main-content {
    padding: 72px 0.75rem 1rem;
  }

  .v0-header {
    height: 60px;
  }

  .v0-logo {
    font-size: 1.5rem;
  }

  .v0-tab-nav {
    padding: 0.25rem;
    margin-bottom: 0.875rem;
    border-radius: 10px;
    overflow-x: visible;
  }

  .v0-tab-item {
    padding: 0.625rem 0.75rem;
    font-size: 0.875rem;
    gap: 0.375rem;
    border-radius: 6px;
    flex: 1;
    min-width: 0;
  }

  .v0-quick-nav {
    grid-template-columns: 1fr;
    gap: 0.625rem;
  }

  .v0-nav-card {
    padding: 1rem;
    border-radius: 12px;
  }

  .v0-nav-card-icon {
    width: 48px;
    height: 48px;
    font-size: 1.5rem;
    border-radius: 10px;
  }

  .v0-nav-card-content h3 {
    font-size: 1rem;
  }

  .v0-nav-card-content p {
    font-size: 0.875rem;
  }

  .v0-sharing-grid {
    grid-template-columns: 1fr;
    gap: 0.625rem;
  }

  .v0-sharing-card {
    padding: 0.875rem;
    border-radius: 10px;
  }

  .v0-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    margin-bottom: 0.625rem;
  }

  .v0-section {
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    border-radius: 8px;
  }

  .v0-section-title {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }

  .v0-card-title {
    font-size: 0.9375rem;
  }

  .v0-card-description {
    font-size: 0.875rem;
  }

  .v0-leaderboard-item {
    padding: 0.75rem;
    gap: 0.75rem;
    border-radius: 10px;
  }

  .v0-leaderboard-list {
    gap: 0.5rem;
  }

  .v0-rank-badge {
    width: 36px;
    height: 36px;
    font-size: 0.8125rem;
  }

  .v0-user-name {
    font-size: 0.9375rem;
  }

  .v0-user-team {
    font-size: 0.8125rem;
  }

  .v0-points {
    font-size: 0.9375rem;
  }

  .v0-contributions {
    font-size: 0.8125rem;
    gap: 0.25rem;
  }

  .v0-quality-badge {
    font-size: 0.5rem;
    padding: 0.0625rem 0.25rem;
    gap: 0.125rem;
  }

  .v0-register-card {
    padding: 1.25rem;
  }

  .v0-register-btn {
    padding: 0.875rem 1.75rem;
    font-size: 0.9375rem;
  }

  .v0-material-link-small {
    font-size: 0.6875rem;
    padding: 0.1875rem 0.375rem;
    gap: 0.125rem;
  }

  /* 移动端简化动画 */
  .v0-nav-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .v0-sharing-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .v0-leaderboard-item:hover {
    box-shadow: 0 1px 4px rgba(102, 126, 234, 0.1);
  }

  .v0-leaderboard-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .v0-leaderboard-subtitle {
    padding: 1rem 1.25rem;
    font-size: 1rem;
  }
}

/* 分享排班日历样式 */
.calendar-header-section {
  margin-bottom: 1.5rem;
}

.calendar-tabs {
  display: flex;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 4px;
  width: fit-content;
}

.calendar-tab {
  padding: 10px 18px;
  border: none;
  background: transparent;
  color: #6b7280;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s ease;

  &.active {
    background: #ffffff;
    color: #1f2937;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  &:hover:not(.active) {
    color: #374151;
  }
}

.month-selector {
  display: flex;
  gap: 8px;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.month-btn {
  padding: 10px 18px;
  border: 1px solid #e5e7eb;
  background: #ffffff;
  color: #6b7280;
  border-radius: 20px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s ease;

  &.active {
    background: #1f2937;
    color: #ffffff;
    border-color: #1f2937;
  }

  &:hover:not(.active) {
    border-color: #d1d5db;
    color: #374151;
  }
}

.schedule-calendar {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.weekdays-header {
  display: grid;
  grid-template-columns: 200px repeat(7, 1fr);
  background: #f8f9fa;
  border-bottom: 1px solid #e5e7eb;
}

.weekday {
  padding: 12px 8px;
  text-align: center;
  font-weight: 500;
  color: #6b7280;
  font-size: 16px;
  border-right: 1px solid #e5e7eb;

  &:last-child {
    border-right: none;
  }
}

.weekday-first {
  padding: 12px 8px;
  text-align: center;
  font-weight: 600;
  color: #374151;
  font-size: 16px;
  background: #f8f9fa;
  border-right: 1px solid #e5e7eb;
}

.calendar-body {
  background: #ffffff;
}

/* 真实日历样式 */
.real-calendar {
  .weekdays-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    margin-bottom: 10px;

    .weekday {
      text-align: center;
      font-weight: 600;
      color: #666;
      padding: 8px;
      background: #f5f5f5;
      border-radius: 4px;
      font-size: 14px;
    }
  }

  .calendar-week {
    position: relative;
    margin-bottom: 8px;

    .week-days {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 1px;

      .date-cell {
        text-align: center;
        padding: 12px 8px;
        background: white;
        border: 1px solid #e8e8e8;
        border-radius: 4px;
        min-height: 90px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        position: relative;

        &.other-month {
          color: #ccc;
          background: #f9f9f9;
        }

        &.has-event {
          background: #e6f3ff;
          border-color: #1890ff;
          font-weight: 600;
        }

        .date-number {
          font-size: 18px;
          font-weight: 600;
          z-index: 10;
          position: relative;
          background: white;
          padding: 3px 8px;
          border-radius: 6px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          margin-bottom: 8px;
        }
      }
    }

    .week-event-bar {
      position: absolute;
      top: 55px;
      left: 6px;
      right: 6px;
      background: linear-gradient(135deg, #1890ff, #40a9ff);
      color: white;
      text-align: center;
      padding: 6px 10px;
      border-radius: 14px;
      font-weight: 600;
      font-size: 14px;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
      z-index: 5;
      height: 28px;
      line-height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.week-row {
  display: grid;
  grid-template-columns: 200px repeat(7, 1fr);
  border-bottom: 1px solid #f3f4f6;
  min-height: 70px;
  position: relative;

  &:last-child {
    border-bottom: none;
  }
}

.week-info {
  padding: 16px 12px;
  background: #f8f9fa;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.week-label {
  font-size: 16px;
  color: #374151;
  font-weight: 600;
  margin-bottom: 6px;
}

.team-info {
  font-size: 15px;
  color: #6b7280;
  font-weight: 500;
}

.date-cell {
  padding: 12px 8px 40px 8px; /* 底部留出空间给事件条 */
  border-right: 1px solid #f3f4f6;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  font-size: 20px;
  font-weight: 600;
  color: #374151;
  min-height: 120px; /* 增加高度以容纳事件条 */
  overflow: hidden;

  &:last-child {
    border-right: none;
  }

  &.empty {
    color: #d1d5db;
  }

  &.has-event {
    /* 去掉蓝色背景和边框 */
  }
}

.date-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
}

.date-number {
  font-size: 20px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;

  .has-event & {
    color: #1f2937;
    font-size: 18px;
  }
}

.share-summary {
  text-align: left;
  width: 100%;
  flex: 1;

  .share-teams {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
  }

  .share-topic {
    font-size: 0.75rem;
    color: #6b7280;
    line-height: 1.3;
    white-space: pre-wrap;
    word-break: break-word;
  }
}

.event-bar {
  position: absolute;
  bottom: 4px;
  left: 4px;
  right: 4px;
  height: 24px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  color: #ffffff;
  font-weight: 600;
  text-align: center;
  line-height: 1;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 1px 3px rgba(59, 130, 246, 0.4);
  z-index: 10;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;

  /* 第1周：从第3列开始，跨3列 (日期2-4) */
  &.teams-1234 {
    left: calc(200px + 2 * (100% - 200px) / 7 + 4px);
    width: calc(3 * (100% - 200px) / 7 - 8px);
  }

  /* 第2周：从第2列开始，跨4列 (日期8-11) */
  &.teams-5678 {
    left: calc(200px + 1 * (100% - 200px) / 7 + 4px);
    width: calc(4 * (100% - 200px) / 7 - 8px);
  }

  /* 第3周：从第2列开始，跨4列 (日期15-18) */
  &.teams-9101112 {
    left: calc(200px + 1 * (100% - 200px) / 7 + 4px);
    width: calc(4 * (100% - 200px) / 7 - 8px);
  }

  /* 第4周：从第2列开始，跨4列 (日期22-25) */
  &.teams-131412 {
    left: calc(200px + 1 * (100% - 200px) / 7 + 4px);
    width: calc(4 * (100% - 200px) / 7 - 8px);
  }
}

/* 合并的周事件条 - 跨越整个一周 */
.week-event-bar {
  position: absolute;
  bottom: 4px;
  left: 208px; /* 从周信息列后开始，留8px间距 */
  right: 8px;
  height: 32px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #ffffff;
  font-weight: 600;
  text-align: center;
  line-height: 1;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  z-index: 5;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 分享排班日历 - 移动端响应式 */
@media (max-width: 768px) {
  .calendar-header-section {
    margin-bottom: 1rem;
  }

  .calendar-tabs {
    width: 100%;
    justify-content: center;
  }

  .calendar-tab {
    flex: 1;
    text-align: center;
  }

  .month-selector {
    justify-content: center;
    margin-bottom: 1.5rem;
  }

  .month-btn {
    padding: 6px 12px;
    font-size: 13px;
  }

  .schedule-calendar {
    border-radius: 8px;
    overflow-x: auto;
  }

  .weekdays-header {
    grid-template-columns: 150px repeat(7, 60px);
  }

  .week-row {
    grid-template-columns: 150px repeat(7, 60px);
    min-height: 60px;
  }

  .week-info {
    padding: 8px 6px;
  }

  .week-label {
    font-size: 11px;
    margin-bottom: 2px;
  }

  .team-info {
    font-size: 10px;
  }

  .date-cell {
    padding: 8px 4px 32px 4px; /* 底部留出空间给事件条 */
    font-size: 12px;
    min-height: 100px; /* 增加高度以容纳事件条 */
  }

  .date-number {
    font-size: 14px;
    margin-bottom: 4px;

    .has-event & {
      font-size: 12px;
    }
  }

  .share-summary {
    .share-teams {
      font-size: 0.75rem;
      margin-bottom: 0.125rem;
    }

    .share-topic {
      font-size: 0.625rem;
      line-height: 1.2;
    }
  }

  .weekday {
    padding: 8px 4px;
    font-size: 12px;
  }

  .weekday-first {
    padding: 8px 4px;
    font-size: 12px;
    font-weight: 600;
    color: #374151;
    background: #f8f9fa;
  }

  .event-bar {
    height: 22px;
    font-size: 11px;
    bottom: 6px;

    /* 移动端事件条位置调整 */
    &.teams-1234 {
      left: calc(150px + 2 * 60px + 2px);
      width: calc(3 * 60px - 4px);
    }

    &.teams-5678 {
      left: calc(150px + 1 * 60px + 2px);
      width: calc(4 * 60px - 4px);
    }

    &.teams-9101112 {
      left: calc(150px + 1 * 60px + 2px);
      width: calc(4 * 60px - 4px);
    }

    &.teams-131412 {
      left: calc(150px + 1 * 60px + 2px);
      width: calc(4 * 60px - 4px);
    }
  }

  /* 移动端合并的周事件条 */
  .week-event-bar {
    bottom: 8px;
    left: 154px; /* 从周信息列后开始，留4px间距 */
    right: 4px;
    height: 36px;
    border-radius: 18px;
    font-size: 16px;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  }
}

/* 积分方案页面样式 */
.points-rules {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-top: 1.5rem;
}

/* 积分规则总览 */
.rule-overview {
  text-align: center;
  padding: 2rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 20px;
  color: white;
  margin-bottom: 1rem;

  .overview-title {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    color: white;
  }

  .overview-desc {
    font-size: 1.125rem;
    opacity: 0.9;
    margin: 0;
  }
}

.rule-card {
  background: #ffffff;
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  /* 扣分规则特殊样式 */
  &.penalty-card {
    border-color: #fecaca;
    background: #fef2f2;

    .rule-title {
      color: #dc2626;
    }
  }

  /* 实用案例特殊样式 */
  &.example-card {
    border-color: #fbbf24;
    background: #fffbeb;

    .rule-title {
      color: #d97706;
    }
  }
}

/* 积分规则表格样式 */
.rule-table {
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
  margin-bottom: 1rem;

  .table-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 2fr;
    background: #f8fafc;
    font-weight: 600;
    color: #374151;
    border-bottom: 2px solid #e2e8f0;

    div {
      padding: 1rem;
      text-align: center;
      border-right: 1px solid #e2e8f0;

      &:last-child {
        border-right: none;
      }
    }
  }

  .table-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 2fr;
    border-bottom: 1px solid #f3f4f6;

    &:last-child {
      border-bottom: none;
    }

    div {
      padding: 1rem;
      text-align: center;
      border-right: 1px solid #f3f4f6;
      display: flex;
      align-items: center;
      justify-content: center;

      &:last-child {
        border-right: none;
      }

      &.col-behavior {
        text-align: left;
        justify-content: flex-start;
        font-weight: 500;
      }

      &.col-personal, &.col-team, &.col-team-only {
        font-weight: 600;
        color: #059669;
      }

      &.col-desc {
        text-align: left;
        justify-content: flex-start;
        font-size: 0.875rem;
        color: #6b7280;
      }
    }
  }

  /* 团队表格特殊布局 */
  &.team-table {
    .table-header {
      grid-template-columns: 2fr 1fr 2fr;
    }

    .table-row {
      grid-template-columns: 2fr 1fr 2fr;
    }
  }
}

.rule-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.rule-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* 规则示例样式 */
.rule-example {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
  font-size: 0.9375rem;
  color: #0c4a6e;

  strong {
    color: #1e40af;
  }
}

/* 规则注释样式 */
.rule-note {
  background: #f3f4f6;
  border-radius: 6px;
  padding: 0.75rem;
  font-size: 0.875rem;
  color: #6b7280;
  text-align: center;
  font-style: italic;
}

/* 实用案例样式 */
.case-example {
  h4 {
    color: #d97706;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
  }

  p {
    color: #374151;
    line-height: 1.6;
    margin: 0;

    strong {
      color: #dc2626;
      font-weight: 700;
    }
  }
}

.rule-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

.rule-label {
  font-size: 0.9375rem;
  color: #475569;
  font-weight: 500;
}

.rule-value {
  font-size: 1rem;
  color: #1e40af;
  font-weight: 600;
}

.detail-list {
  margin: 0.75rem 0 0 1.5rem;

  li {
    color: #64748b;
    font-size: 0.9375rem;
    margin-bottom: 0.5rem;
    line-height: 1.5;
  }
}

/* 奖品页面样式 */
.rewards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.reward-card {
  background: #ffffff;
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

.reward-image {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.reward-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.75rem;
}

.reward-points {
  font-size: 1.125rem;
  font-weight: 600;
  color: #dc2626;
  margin-bottom: 0.75rem;
}

.reward-desc {
  font-size: 0.9375rem;
  color: #64748b;
  line-height: 1.5;
  margin-bottom: 1.5rem;
}

.reward-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.9375rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;

  &:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

/* 排行榜API集成样式 */
.leaderboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.refresh-btn {
 
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 6px 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;

 

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
 

  p {
    margin-top: 1rem;
    font-size: 0.875rem;
  }
}

.error-message {
  text-align: center;
  padding: 1.5rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;

  p {
    margin-bottom: 1rem;
    font-size: 0.875rem;
  }

  button {
    background: #dc2626;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.875rem;

    &:hover {
      background: #b91c1c;
    }
  }
}

/* 无数据状态样式 */
.no-data {
  text-align: center;
  padding: 3rem 1rem;
  color: #e8eaee;

  p {
    font-size: 1rem;
    margin: 0;
  }
}

/* 积分规则移动端响应式 */
@media (max-width: 768px) {
  .rule-overview {
    padding: 1.5rem;

    .overview-title {
      font-size: 1.5rem;
    }

    .overview-desc {
      font-size: 1rem;
    }
  }

  .rule-table {
    .table-header {
      grid-template-columns: 1fr;

      div {
        padding: 0.75rem;
        border-right: none;
        border-bottom: 1px solid #e2e8f0;

        &:last-child {
          border-bottom: none;
        }
      }
    }

    .table-row {
      grid-template-columns: 1fr;

      div {
        padding: 0.75rem;
        border-right: none;
        border-bottom: 1px solid #f3f4f6;
        text-align: left !important;
        justify-content: flex-start !important;

        &:last-child {
          border-bottom: none;
        }

        &.col-personal, &.col-team, &.col-team-only {
          font-size: 1.125rem;
          color: #059669;
          font-weight: 700;
        }
      }
    }

    &.team-table {
      .table-header {
        grid-template-columns: 1fr;
      }

      .table-row {
        grid-template-columns: 1fr;
      }
    }
  }

  .rule-card {
    padding: 1rem;
  }

  .rule-title {
    font-size: 1.125rem;
  }

  .case-example {
    h4 {
      font-size: 0.9375rem;
    }

    p {
      font-size: 0.875rem;
    }
  }
}

/* 新的日历容器样式 */
.calendar-container {
  background: #f8f9fa;
  border-radius: 12px;
  overflow: hidden;
  padding: 8px;

  .calendar-week {
    position: relative;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 4px;

    .week-days {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 0;

      .date-cell {
        text-align: center;
        padding: 12px 8px;
        background: white;
        border-right: 1px solid #f0f0f0;
        min-height: 90px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        position: relative;

        &:last-child {
          border-right: none;
        }

        &.other-month {
          color: #ccc;
          background: #f9f9f9;

          .date-number {
            color: #ccc;
          }
        }

        &.has-event {
          background: #f0f8ff;
        }

        .date-number {
          font-size: 24px;
          font-weight: 600;
          color: #333;
          z-index: 10;
          position: relative;
          background: white;
          padding: 6px 10px;
          border-radius: 8px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          margin-bottom: 8px;
        }
      }
    }

    .week-event-bar {
      position: absolute;
      top: 65px;
      left: 8px;
      right: 8px;
      background: linear-gradient(135deg, #1890ff, #40a9ff);
      color: white;
      text-align: center;
      padding: 6px 12px;
      border-radius: 14px;
      font-weight: 600;
      font-size: 14px;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
      z-index: 5;
      height: 28px;
      line-height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  /* 日历说明文字 */
  .calendar-notice {
    margin-top: 1.5rem;
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-radius: 12px;
    border: 1px solid #bae6fd;
    text-align: center;

    p {
      margin: 0;
      font-size: 1rem;
      color: #0369a1;
      font-weight: 500;
      line-height: 1.5;
    }
  }
}

/* 首页即将分享和历史分享样式 - 横向布局 */
.home-sharing-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-top: 2rem;
}

.section-header {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1.5rem;

  .section-subtitle {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
  }
}

.upcoming-section,
.history-section {
  position: relative;
  overflow: hidden;
}

.upcoming-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(147, 197, 253, 0.01) 100%);
  pointer-events: none;
}

.history-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.02) 0%, rgba(110, 231, 183, 0.01) 100%);
  pointer-events: none;
}

/* 本场分享信息样式 */
.current-session-info {
  margin-bottom: 1.5rem;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  border: 1px solid #bae6fd;
}

.session-meta {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.session-time,
.session-location {
  font-size: 0.9375rem;
  color: #0369a1;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* 当前分享卡片样式 */
.current-sharing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.current-sharing-card {
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  padding: 1.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.current-sharing-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
  border-color: #3b82f6;
}

.current-sharing-card .card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.current-sharing-card .card-description {
  font-size: 0.9375rem;
  color: #64748b;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.current-sharing-card .card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.current-sharing-card .card-speaker {
  font-size: 0.875rem;
  color: #475569;
  font-weight: 500;
}

.current-sharing-card .material-link {
  padding: 0.375rem 0.75rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
  text-decoration: none;
  border-radius: 8px;
  font-size: 0.8125rem;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.current-sharing-card .material-link:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* 查看分享日历按钮样式 */
.calendar-link-section {
  margin-top: 1.5rem;
}

.calendar-link-btn {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
  padding: 1.25rem 1.5rem;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 2px solid #bae6fd;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: left;
  font-family: inherit;
  position: relative;
  overflow: hidden;
}

.calendar-link-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 197, 253, 0.03) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.calendar-link-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
  border-color: #3b82f6;
}

.calendar-link-btn:hover::before {
  opacity: 1;
}

.calendar-icon {
  font-size: 2rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  z-index: 2;
  position: relative;
}

.calendar-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  z-index: 2;
  position: relative;
}

.calendar-title {
  font-size: 1.125rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.3;
}

.calendar-subtitle {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
  line-height: 1.4;
}

.calendar-arrow {
  font-size: 1.25rem;
  color: #3b82f6;
  font-weight: 600;
  transition: transform 0.3s ease;
  z-index: 2;
  position: relative;
}

.calendar-link-btn:hover .calendar-arrow {
  transform: translateX(4px);
}

.upcoming-sharing-list,
.history-sharing-list {
  display: flex;
  flex-direction: row;
  gap: 1rem;
  overflow-x: auto;
  padding: 0.5rem 0 1rem 0;
  scroll-behavior: smooth;
}

.upcoming-sharing-list::-webkit-scrollbar,
.history-sharing-list::-webkit-scrollbar {
  height: 6px;
}

.upcoming-sharing-list::-webkit-scrollbar-track,
.history-sharing-list::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.upcoming-sharing-list::-webkit-scrollbar-thumb,
.history-sharing-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.upcoming-sharing-list::-webkit-scrollbar-thumb:hover,
.history-sharing-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.upcoming-item,
.history-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  padding: 1.25rem;
  background: #ffffff;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1;
  min-width: 200px;
  flex-shrink: 0;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.upcoming-item:hover,
.history-item:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 30px rgba(59, 130, 246, 0.15);
  border-color: #3b82f6;
}

.upcoming-date,
.history-date {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 70px;
  height: 70px;
  border-radius: 16px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
  font-weight: 600;
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
  margin-bottom: 0.5rem;
}

.history-date {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.date-day {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
}

.date-month {
  font-size: 0.8125rem;
  opacity: 0.9;
  line-height: 1;
  margin-top: 2px;
}

.upcoming-content,
.history-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}

.upcoming-team,
.history-team {
  font-size: 1.0625rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.3;
}

.upcoming-time,
.history-time {
  font-size: 0.8125rem;
  color: #64748b;
  font-weight: 500;
  line-height: 1.3;
}

.upcoming-status,
.history-status {
  display: flex;
  justify-content: center;
  margin-top: 0.25rem;
}

.status-badge {
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
}

.status-badge.upcoming {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);
}

.status-badge.completed {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
  color: #64748b;

  .no-data-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.6;
  }

  p {
    margin: 0;
    font-size: 1rem;
    font-weight: 500;
  }
}

/* 移动端响应式 */
@media (max-width: 768px) {
  .home-sharing-sections {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-top: 1.5rem;
  }

  .upcoming-item,
  .history-item {
    padding: 0.875rem;
    gap: 0.875rem;
  }

  .upcoming-date,
  .history-date {
    width: 50px;
    height: 50px;
  }

  .date-day {
    font-size: 1.125rem;
  }

  .date-month {
    font-size: 0.6875rem;
  }

  .upcoming-team,
  .history-team {
    font-size: 0.9375rem;
  }

  .upcoming-time,
  .history-time {
    font-size: 0.8125rem;
  }

  .status-badge {
    padding: 0.25rem 0.5rem;
    font-size: 0.6875rem;
  }

  .no-data-message {
    padding: 2rem 1rem;

    .no-data-icon {
      font-size: 2.5rem;
    }

    p {
      font-size: 0.875rem;
    }
  }
}

/* 简化的分享报名表单样式 */
.simplified-form {
  max-width: 600px;
  margin: 0 auto;
  padding: 0;
  background: transparent;
}

.form-field {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.form-input {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  line-height: 1.5;
  color: #374151;
  background-color: #ffffff;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input::placeholder {
  color: #9ca3af;
  font-size: 0.9375rem;
  line-height: 1.4;
}

.form-input[type="url"] {
  font-family: inherit;
}

select.form-input {
  cursor: pointer;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
  padding-right: 2.5rem;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

select.form-input:focus {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233b82f6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

textarea.form-input {
  resize: vertical;
  min-height: 150px;
  line-height: 1.6;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.submit-btn,
.reset-btn {
  padding: 0.875rem 2rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  min-width: 120px;
}

.submit-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

.reset-btn {
  background: #ffffff;
  color: #6b7280;
  border-color: #d1d5db;
}

.reset-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
  color: #374151;
}

/* 移动端响应式 */
@media (max-width: 768px) {
  .simplified-form {
    max-width: 100%;
    padding: 0 1rem;
  }

  .form-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .submit-btn,
  .reset-btn {
    width: 100%;
    min-width: auto;
  }
}
