import { useState, useRef, useEffect } from 'react';
import './style.css';

function BaoMing() {
  // 状态管理
  const [selectedDate, setSelectedDate] = useState(null);
  const [showCalendar, setShowCalendar] = useState(false);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [docLink, setDocLink] = useState('');
  const [linkStatus, setLinkStatus] = useState({
    show: false,
    type: '',
    message: ''
  });
  const [files, setFiles] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [isDragActive, setIsDragActive] = useState(false);

  // refs
  const calendarRef = useRef(null);
  const dateInputRef = useRef(null);
  const fileInputRef = useRef(null);

  // 文件处理函数
  const handleFiles = (newFiles) => {
    const validTypes = [
      'application/pdf',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];

    const validFiles = [];
    for (let i = 0; i < newFiles.length; i += 1) {
      const file = newFiles[i];

      if (!validTypes.includes(file.type)) {
        // eslint-disable-next-line no-alert
        alert(
          `文件 "${file.name}" 格式不支持，请上传PDF, PPT, DOC或DOCX文件。`
        );
        // eslint-disable-next-line no-continue
        continue;
      }

      if (file.size > 10 * 1024 * 1024) {
        // eslint-disable-next-line no-alert
        alert(`文件 "${file.name}" 太大，最大支持10MB。`);
        // eslint-disable-next-line no-continue
        continue;
      }

      validFiles.push(file);
    }

    setFiles((prev) => [...prev, ...validFiles]);
  };

  const removeFile = (index) => {
    setFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const getFileIcon = (file) => {
    if (file.type.includes('pdf')) return 'fas fa-file-pdf';
    if (file.type.includes('powerpoint') || file.type.includes('presentation'))
      return 'fas fa-file-powerpoint';
    if (file.type.includes('word') || file.type.includes('msword'))
      return 'fas fa-file-word';
    return 'fas fa-file';
  };

  // 日期格式化
  const formatDate = (date) => {
    if (!date) return '';
    return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
  };

  // 日历渲染
  const renderCalendar = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const firstDayOfMonth = new Date(year, month, 1);
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const startingDay = firstDayOfMonth.getDay();
    const today = new Date();

    const days = [];

    // 上个月的日期
    const prevMonthDays = new Date(year, month, 0).getDate();
    for (let i = 0; i < startingDay; i += 1) {
      days.push(
        <button
          key={`prev-${i}`}
          type="button"
          className="calendar-day disabled"
          disabled
        >
          {prevMonthDays - startingDay + i + 1}
        </button>
      );
    }

    // 当前月的日期
    for (let i = 1; i <= daysInMonth; i += 1) {
      const date = new Date(year, month, i);
      const isToday = date.toDateString() === today.toDateString();
      const isSelected =
        selectedDate && date.toDateString() === selectedDate.toDateString();
      const isPast = date < today;

      days.push(
        <button
          key={i}
          type="button"
          className={`calendar-day ${isSelected ? 'selected' : ''} ${
            isPast ? 'disabled' : ''
          } ${isToday ? 'today' : ''}`}
          disabled={isPast}
          onClick={() => {
            if (!isPast) {
              setSelectedDate(date);
              setShowCalendar(false);
            }
          }}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              if (!isPast) {
                setSelectedDate(date);
                setShowCalendar(false);
              }
            }
          }}
        >
          {i}
        </button>
      );
    }

    return days;
  };

  // 链接验证
  const handleVerifyLink = async () => {
    if (!docLink.trim()) {
      setLinkStatus({
        show: true,
        type: 'warning',
        message: '请输入有效的URL'
      });
      return;
    }

    setIsVerifying(true);

    // 模拟验证过程
    setTimeout(() => {
      const isValid = Math.random() > 0.3; // 70%的成功率模拟
      setLinkStatus({
        show: true,
        type: isValid ? 'success' : 'error',
        message: isValid ? '链接验证成功' : '链接无效或无法访问'
      });
      setIsVerifying(false);
    }, 1500);
  };

  // 拖拽处理
  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragActive(true);
  };

  const handleDragLeave = () => {
    setIsDragActive(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragActive(false);
    if (e.dataTransfer.files.length) {
      handleFiles(Array.from(e.dataTransfer.files));
    }
  };

  const handleFileSelect = (e) => {
    if (e.target.files.length) {
      handleFiles(Array.from(e.target.files));
    }
  };

  // 表单提交
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!selectedDate) {
      // eslint-disable-next-line no-alert
      alert('请选择分享日期');
      return;
    }

    if (!docLink && files.length === 0) {
      // eslint-disable-next-line no-alert
      alert('请提供分享文档链接或上传文件');
      return;
    }

    setIsSubmitting(true);

    // 模拟提交过程
    setTimeout(() => {
      // eslint-disable-next-line no-console
      console.log('表单数据:', {
        shareDate: formatDate(selectedDate),
        docLink,
        files: files.map((f) => f.name)
      });

      setShowSuccessModal(true);
      setIsSubmitting(false);
    }, 2000);
  };

  // 重置表单
  const resetForm = () => {
    setSelectedDate(null);
    setDocLink('');
    setLinkStatus({ show: false, type: '', message: '' });
    setFiles([]);
    setShowSuccessModal(false);
    setShowCalendar(false);
  };

  // 点击外部关闭日历
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target) &&
        !dateInputRef.current.contains(event.target)
      ) {
        setShowCalendar(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const getIconClass = (type) => {
    switch (type) {
      case 'success':
        return 'fa-check-circle';
      case 'error':
        return 'fa-times-circle';
      case 'warning':
        return 'fa-exclamation-circle';
      default:
        return 'fa-info-circle';
    }
  };

  const handlePrevMonth = () => {
    setCurrentDate(
      new Date(currentDate.getFullYear(), currentDate.getMonth() - 1)
    );
  };

  const handleNextMonth = () => {
    setCurrentDate(
      new Date(currentDate.getFullYear(), currentDate.getMonth() + 1)
    );
  };

  return (
    <>
      {/* Font Awesome CSS */}
      <link
        rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      />

      <div className="baoming-page">
        <div className="baoming-container">
          <div className="baoming-card">
            <div className="baoming-header">
              <h1 className="baoming-title">文档分享</h1>
              <p className="baoming-subtitle">选择分享时间并上传文档</p>
            </div>

            <form onSubmit={handleSubmit} className="baoming-form">
              {/* 日期选择 */}
              <div className="date-picker">
                {/* eslint-disable-next-line jsx-a11y/label-has-associated-control */}
                <label htmlFor="shareDate" className="form-label">
                  分享日期
                </label>
                <div style={{ position: 'relative' }}>
                  <input
                    ref={dateInputRef}
                    type="text"
                    id="shareDate"
                    className="form-input"
                    placeholder="点击选择日期"
                    value={formatDate(selectedDate)}
                    onClick={() => setShowCalendar(!showCalendar)}
                    readOnly
                    style={{ cursor: 'pointer', paddingRight: '40px' }}
                  />
                  <div
                    style={{
                      position: 'absolute',
                      right: '12px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      pointerEvents: 'none'
                    }}
                  >
                    <i
                      className="fas fa-calendar-alt"
                      style={{ color: '#9ca3af' }}
                    />
                  </div>
                </div>

                {showCalendar && (
                  <div ref={calendarRef} className="calendar">
                    <div className="calendar-header">
                      <button
                        type="button"
                        onClick={handlePrevMonth}
                        style={{
                          padding: '0.5rem',
                          borderRadius: '50%',
                          border: 'none',
                          background: 'transparent',
                          cursor: 'pointer'
                        }}
                        aria-label="上一月"
                      >
                        <i className="fas fa-chevron-left" />
                      </button>
                      <h3 style={{ fontWeight: '500', margin: 0 }}>
                        {`${currentDate.getFullYear()}年${
                          currentDate.getMonth() + 1
                        }月`}
                      </h3>
                      <button
                        type="button"
                        onClick={handleNextMonth}
                        style={{
                          padding: '0.5rem',
                          borderRadius: '50%',
                          border: 'none',
                          background: 'transparent',
                          cursor: 'pointer'
                        }}
                        aria-label="下一月"
                      >
                        <i className="fas fa-chevron-right" />
                      </button>
                    </div>
                    <div className="calendar-weekdays">
                      {['日', '一', '二', '三', '四', '五', '六'].map((day) => (
                        <div key={day} className="calendar-weekday">
                          {day}
                        </div>
                      ))}
                    </div>
                    <div className="calendar-grid">{renderCalendar()}</div>
                  </div>
                )}
              </div>

              {/* 文档上传 */}
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '16px'
                }}
              >
                {/* eslint-disable-next-line jsx-a11y/label-has-associated-control */}
                <label htmlFor="docLink" className="form-label">
                  上传文档链接
                </label>
                <div style={{ display: 'flex', gap: '8px' }}>
                  <input
                    id="docLink"
                    type="url"
                    className="form-input"
                    placeholder="https://example.com/document.pdf"
                    value={docLink}
                    onChange={(e) => setDocLink(e.target.value)}
                    style={{ flex: 1 }}
                  />
                  <button
                    type="button"
                    className="btn-secondary"
                    onClick={handleVerifyLink}
                    disabled={isVerifying}
                  >
                    {isVerifying ? (
                      <>
                        <i
                          className="fas fa-spinner fa-spin"
                          style={{ marginRight: '4px' }}
                        />
                        验证中...
                      </>
                    ) : (
                      '验证链接'
                    )}
                  </button>
                </div>

                {linkStatus.show && (
                  <div className={`link-status ${linkStatus.type}`}>
                    <i
                      className={`fas ${getIconClass(linkStatus.type)}`}
                      style={{ marginRight: '4px' }}
                    />
                    {linkStatus.message}
                  </div>
                )}

                <div>
                  {/* eslint-disable-next-line jsx-a11y/label-has-associated-control */}
                  <label htmlFor="fileUpload" className="form-label">
                    或上传本地文件
                  </label>
                  <div
                    className={`file-upload ${isDragActive ? 'active' : ''}`}
                    onClick={() => fileInputRef.current?.click()}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onDrop={handleDrop}
                    role="button"
                    tabIndex={0}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        fileInputRef.current?.click();
                      }
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}
                    >
                      <i
                        className="fas fa-cloud-upload-alt"
                        style={{
                          fontSize: '48px',
                          color: '#3b82f6',
                          marginBottom: '8px'
                        }}
                      />
                      <p
                        style={{
                          color: '#6b7280',
                          fontWeight: '500',
                          margin: 0
                        }}
                      >
                        拖放文件到此处或点击上传
                      </p>
                      <p
                        style={{
                          color: '#9ca3af',
                          fontSize: '14px',
                          marginTop: '4px',
                          margin: 0
                        }}
                      >
                        支持PDF, PPT, DOC, DOCX格式
                      </p>
                      <input
                        ref={fileInputRef}
                        id="fileUpload"
                        type="file"
                        style={{ display: 'none' }}
                        accept=".pdf,.ppt,.pptx,.doc,.docx"
                        multiple
                        onChange={handleFileSelect}
                      />
                    </div>
                  </div>

                  {files.length > 0 && (
                    <div className="file-list">
                      {files.map((file, index) => (
                        <div
                          key={`${file.name}-${file.size}-${file.lastModified}`}
                          className="file-item"
                        >
                          <i className={`file-icon ${getFileIcon(file)}`} />
                          <span className="file-name">{file.name}</span>
                          <button
                            type="button"
                            className="file-remove fas fa-times"
                            onClick={() => removeFile(index)}
                            aria-label={`删除文件 ${file.name}`}
                          />
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* 提交按钮 */}
              <div style={{ paddingTop: '16px' }}>
                <button
                  type="submit"
                  className="btn-primary"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <i
                        className="fas fa-spinner fa-spin"
                        style={{ marginRight: '4px' }}
                      />
                      提交中...
                    </>
                  ) : (
                    '提交分享'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>

        {/* 成功模态框 */}
        <div className={`modal-overlay ${showSuccessModal ? '' : 'hidden'}`}>
          <div className="modal-content">
            <div className="modal-header">
              <div className="modal-icon">
                <i className="fas fa-check" />
              </div>
              <h3 className="modal-title">分享提交成功!</h3>
              <div className="modal-text">
                <p>您的文档分享已成功提交。</p>
              </div>
              <div className="modal-actions">
                <button
                  type="button"
                  className="btn-primary"
                  onClick={resetForm}
                >
                  关闭
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default BaoMing;
