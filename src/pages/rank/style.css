/* 报名页面样式 */
.baoming-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  padding: 2rem 1rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.baoming-container {
  max-width: 500px;
  margin: 0 auto;
}

.baoming-card {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  padding: 2rem;
}

.baoming-header {
  text-align: center;
  margin-bottom: 2rem;
}

.baoming-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.baoming-subtitle {
  color: #6b7280;
  font-size: 1rem;
  margin: 0;
}

.baoming-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* 表单元素样式 */
.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: all 0.2s;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 按钮样式 */
.btn-primary {
  width: 100%;
  padding: 0.75rem 1rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  padding: 0.75rem 1rem;
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.btn-secondary:hover:not(:disabled) {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.btn-secondary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 日期选择器样式 */
.date-picker {
  position: relative;
}

.calendar {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: 10;
  padding: 1rem;
  margin-top: 0.25rem;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.25rem;
  margin-bottom: 0.5rem;
}

.calendar-weekday {
  text-align: center;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  padding: 0.5rem;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.25rem;
}

.calendar-day {
  text-align: center;
  padding: 0.5rem;
  border-radius: 0.25rem;
  cursor: pointer;
  border: none;
  background: transparent;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.calendar-day:hover:not(:disabled) {
  background-color: #f3f4f6;
}

.calendar-day.selected {
  background-color: #3b82f6;
  color: white;
}

.calendar-day.today {
  font-weight: 600;
  color: #3b82f6;
}

.calendar-day.disabled {
  color: #d1d5db;
  cursor: not-allowed;
}

/* 链接状态样式 */
.link-status {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

.link-status.success {
  color: #059669;
}

.link-status.error {
  color: #dc2626;
}

.link-status.warning {
  color: #d97706;
}

/* 文件上传样式 */
.file-upload {
  border: 2px dashed #d1d5db;
  border-radius: 0.5rem;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  background: #fafafa;
}

.file-upload:hover {
  border-color: #3b82f6;
  background-color: #f8fafc;
}

.file-upload.active {
  border-color: #3b82f6;
  background-color: #f0f9ff;
}

.file-list {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

.file-icon {
  margin-right: 0.75rem;
  color: #6b7280;
  font-size: 1.125rem;
}

.file-name {
  flex-grow: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 0.875rem;
  color: #374151;
}

.file-remove {
  color: #ef4444;
  cursor: pointer;
  padding: 0.25rem;
  border: none;
  background: transparent;
  border-radius: 0.25rem;
  transition: all 0.2s;
}

.file-remove:hover {
  background-color: #fee2e2;
  color: #dc2626;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
  padding: 1rem;
}

.modal-overlay.hidden {
  display: none;
}

.modal-content {
  background: white;
  border-radius: 0.75rem;
  padding: 2rem;
  max-width: 400px;
  width: 100%;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
}

.modal-header {
  text-align: center;
}

.modal-icon {
  margin: 0 auto 1rem;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: #d1fae5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #059669;
  font-size: 1.5rem;
}

.modal-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.modal-text {
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 1.5rem;
}

.modal-text p {
  margin: 0;
}

.modal-actions {
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .baoming-page {
    padding: 1rem 0.5rem;
  }

  .baoming-card {
    padding: 1.5rem;
  }

  .baoming-title {
    font-size: 1.75rem;
  }

  .file-upload {
    padding: 1.5rem;
  }

  .modal-content {
    padding: 1.5rem;
  }
}
