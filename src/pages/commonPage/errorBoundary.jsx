import React from 'react';
import { Result } from 'antd';
import { useRouteError, isRouteErrorResponse } from 'react-router-dom';
import Route404 from './route404';

export default function ErrorBoundary() {
  const error = useRouteError();
  return isRouteErrorResponse(error) ? ( // 路由错误展示
    <Route404 />
  ) : (
    // 页面运行错误展示
    <Result
      status="500"
      title="500"
      subTitle="服务器好像出了点问题，报错信息如下："
      extra={<div style={{ color: '#f6613c' }}>{error.message || error}</div>} // 仅作示例，请避免写内联样式
    />
  );
}
