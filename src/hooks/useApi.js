import { useState, useEffect, useCallback } from 'react';
import { message } from 'antd';

// 通用API Hook
export const useApi = (apiFunction, dependencies = [], options = {}) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const {
    immediate = true,
    onSuccess,
    onError,
    showErrorMessage = true
  } = options;

  const execute = useCallback(
    async (...args) => {
      try {
        setLoading(true);
        setError(null);

        const result = await apiFunction(...args);

        setData(result);

        if (onSuccess) {
          onSuccess(result);
        }

        return result;
      } catch (err) {
        setError(err);

        if (showErrorMessage) {
          message.error(err.message || '请求失败');
        }

        if (onError) {
          onError(err);
        }

        throw err;
      } finally {
        setLoading(false);
      }
    },
    [apiFunction, onSuccess, onError, showErrorMessage]
  );

  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, [immediate, execute, ...dependencies]);

  const refresh = useCallback(() => {
    execute();
  }, [execute]);

  return {
    data,
    loading,
    error,
    execute,
    refresh
  };
};

// 排行榜Hook
export const useLeaderboard = (type = 'personal', options = {}) => {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const apiFunction = useCallback(async () => {
    console.log(`useLeaderboard: 调用API获取${type}排行榜数据...`);
    const apiService = (await import('../utils/api/services')).default;
    const result = await apiService.getLeaderboard(type, page, pageSize);
    console.log(`useLeaderboard: ${type}排行榜数据获取成功:`, result);
    return result;
  }, [type, page, pageSize]);

  const result = useApi(apiFunction, [type, page, pageSize], options);

  return {
    ...result,
    page,
    setPage,
    pageSize,
    setPageSize
  };
};

// 分享列表Hook
export const useShares = (filters = {}, options = {}) => {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const apiFunction = useCallback(async () => {
    const apiService = (await import('../utils/api/services')).default;
    return apiService.getShares(page, pageSize, filters);
  }, [page, pageSize, filters]);

  const result = useApi(apiFunction, [page, pageSize, filters], options);

  return {
    ...result,
    page,
    setPage,
    pageSize,
    setPageSize
  };
};

// 分享日历Hook
export const useShareCalendar = (year, month, options = {}) => {
  const apiFunction = useCallback(async () => {
    const apiService = (await import('../utils/api/services')).default;
    return apiService.getShareCalendar(year, month);
  }, [year, month]);

  return useApi(apiFunction, [year, month], options);
};

// 积分历史Hook
export const usePointsHistory = (userId, options = {}) => {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  const apiFunction = useCallback(async () => {
    const apiService = (await import('../utils/api/services')).default;
    return apiService.getPointsHistory(userId, page, pageSize);
  }, [userId, page, pageSize]);

  const result = useApi(apiFunction, [userId, page, pageSize], options);

  return {
    ...result,
    page,
    setPage,
    pageSize,
    setPageSize
  };
};

// 奖品列表Hook
export const useRewards = (options = {}) => {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  const apiFunction = useCallback(async () => {
    const apiService = (await import('../utils/api/services')).default;
    return apiService.getRewards(page, pageSize);
  }, [page, pageSize]);

  const result = useApi(apiFunction, [page, pageSize], options);

  return {
    ...result,
    page,
    setPage,
    pageSize,
    setPageSize
  };
};

// 用户信息Hook
export const useCurrentUser = (options = {}) => {
  const apiFunction = useCallback(async () => {
    const apiService = (await import('../utils/api/services')).default;
    return apiService.getCurrentUser();
  }, []);

  return useApi(apiFunction, [], options);
};

// 统计数据Hook
export const useStatistics = (type = 'overview', options = {}) => {
  const apiFunction = useCallback(async () => {
    const apiService = (await import('../utils/api/services')).default;
    return apiService.getStatistics(type);
  }, [type]);

  return useApi(apiFunction, [type], options);
};
