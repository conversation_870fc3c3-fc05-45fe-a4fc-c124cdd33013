const responseHandler = (response) => {
  const { code, data } = response;
  // 未登录
  if (code === -310) {
    const loginpath = data;
    const { pathname, href, origin } = window.location;
    let rurl = href;
    if (pathname === '/logout') {
      // 对通过url退出登录做重定向链接处理
      rurl = origin;
    }
    // 获取当前页的前端全路径
    rurl = encodeURIComponent(encodeURIComponent(rurl));
    // 用参数rurl拼在一起。注意参数名"rurl"不可变
    const service = `${loginpath}?rurl=${rurl}`;
    // 前端负责跳转
    window.location.href = service;
    return null;
  }
  return response;
};

export default responseHandler;
