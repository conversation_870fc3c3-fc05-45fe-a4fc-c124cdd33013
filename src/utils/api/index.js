import { message } from 'antd';
import responseHandler from '../responseHandler'; // 用于判断登录态以及登录重定向

// fetch示例：
export default (reqLink) =>
  new Promise((resolve, reject) => {
    fetch(reqLink, {
      method: 'GET',
      headers: {
        'x-requested-with': 'XMLHttpRequest' // keboot组件实现的登录功能必带header
      }
    })
      .then((response) => response.json())
      .then((res) => {
        const handlerRes = responseHandler(res, reject); // 根据返回结果进行登录判断和重定向处理
        resolve(handlerRes);
      })
      .catch((error) => message.error(error));
  });
