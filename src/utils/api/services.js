// API服务配置文件
const API_BASE_URL = 'http://sparks.ttb.test.ke.com';

// HTTP请求工具函数
const request = async (url, options = {}) => {
  const config = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  };

  try {
    console.log(`发起API请求: ${url}`);
    const response = await fetch(url, config);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log(`API响应成功: ${url}`, data);
    return data;
  } catch (error) {
    console.error(`API请求失败: ${url}`, error);
    throw error;
  }
};

// 备用模拟数据（当API失败时使用）
const fallbackData = {
  // 个人排行榜数据
  personalLeaderboard: {
    data: [
      {
        userId: 1,
        id: 1,
        name: '李琦(七小贝)',
        teamId: 1,
        team: '1队',
        points: 285,
        rank: 1,
        shareCount: 12,
        contributions: 12,
        qualityCount: 3
      },
      {
        userId: 2,
        id: 2,
        name: '曾谦(言千)',
        teamId: 2,
        team: '2队',
        points: 270,
        rank: 2,
        shareCount: 11,
        contributions: 11,
        qualityCount: 2
      },
      {
        userId: 3,
        id: 3,
        name: '陈昌永(卡内基)',
        teamId: 3,
        team: '3队',
        points: 255,
        rank: 3,
        shareCount: 10,
        contributions: 10,
        qualityCount: 2
      },
      {
        userId: 4,
        id: 4,
        name: '陈香玉(chen)',
        teamId: 4,
        team: '4队',
        points: 240,
        rank: 4,
        shareCount: 9,
        contributions: 9,
        qualityCount: 1
      },
      {
        userId: 5,
        id: 5,
        name: '陈雅(清零辣)',
        teamId: 5,
        team: '5队',
        points: 225,
        rank: 5,
        shareCount: 8,
        contributions: 8,
        qualityCount: 1
      }
    ]
  },

  // 团队排行榜数据
  teamLeaderboard: {
    data: [
      {
        teamId: 1,
        id: 1,
        teamName: '技术创新队',
        name: '技术创新队',
        totalPoints: 1250,
        points: 1250,
        rank: 1,
        shareCount: 45,
        contributions: 45
      },
      {
        teamId: 2,
        id: 2,
        teamName: '产品优化队',
        name: '产品优化队',
        totalPoints: 1180,
        points: 1180,
        rank: 2,
        shareCount: 42,
        contributions: 42
      },
      {
        teamId: 3,
        id: 3,
        teamName: '设计体验队',
        name: '设计体验队',
        totalPoints: 1120,
        points: 1120,
        rank: 3,
        shareCount: 38,
        contributions: 38
      },
      {
        teamId: 4,
        id: 4,
        teamName: '运营推广队',
        name: '运营推广队',
        totalPoints: 1050,
        points: 1050,
        rank: 4,
        shareCount: 35,
        contributions: 35
      },
      {
        teamId: 5,
        id: 5,
        teamName: '数据分析队',
        name: '数据分析队',
        totalPoints: 980,
        points: 980,
        rank: 5,
        shareCount: 32,
        contributions: 32
      }
    ]
  },

  // 分享排班数据
  shareSchedule: [
    {
      date_range: '2024-06-15',
      team_name: '技术创新队',
      team: '1队',
      topic: 'AI工具在开发中的应用',
      presenter: '李琦(七小贝)'
    },
    {
      date_range: '2024-06-22',
      team_name: '产品优化队',
      team: '2队',
      topic: '用户体验优化实践',
      presenter: '曾谦(言千)'
    },
    {
      date_range: '2024-06-29',
      team_name: '设计体验队',
      team: '3队',
      topic: '设计系统构建经验',
      presenter: '陈昌永(卡内基)'
    },
    {
      date_range: '2024-07-06',
      team_name: '运营推广队',
      team: '4队',
      topic: '数据驱动的运营策略',
      presenter: '陈香玉(chen)'
    },
    {
      date_range: '2024-07-13',
      team_name: '数据分析队',
      team: '5队',
      topic: '机器学习在业务中的应用',
      presenter: '陈雅(清零辣)'
    }
  ]
};

// API服务类
class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  // 模拟网络延迟
  static async delay(ms = 500) {
    return new Promise((resolve) => {
      setTimeout(resolve, ms);
    });
  }

  // 获取排行榜（统一接口）
  async getLeaderboard(type = 'personal') {
    console.log(`获取${type}排行榜数据...`);

    try {
      let url;
      if (type === 'personal') {
        url = `${this.baseURL}/app_aishare/members/all_score/`;
      } else if (type === 'team') {
        url = `${this.baseURL}/app_aishare/teams/all_score/`;
      } else {
        throw new Error(`不支持的排行榜类型: ${type}`);
      }

      const data = await request(url);
      console.log(`${type}排行榜API响应:`, data);
      return data;
    } catch (error) {
      console.error(`获取${type}排行榜失败，使用备用数据:`, error);
      // API失败时使用备用数据
      if (type === 'personal') {
        return fallbackData.personalLeaderboard;
      }
      if (type === 'team') {
        return fallbackData.teamLeaderboard;
      }
      return { data: [] };
    }
  }

  // 获取个人排行榜
  async getPersonalLeaderboard() {
    await ApiService.delay();
    return fallbackData.personalLeaderboard;
  }

  // 获取团队排行榜
  async getTeamLeaderboard() {
    await ApiService.delay();
    return fallbackData.teamLeaderboard;
  }

  // 获取分享排班
  async getShareSchedule() {
    console.log('获取分享排班数据...');

    try {
      const url = `${this.baseURL}/app_aishare/schedule/list/`;
      const data = await request(url);
      console.log('分享排班API响应:', data);
      return data;
    } catch (error) {
      console.error('获取分享排班失败，使用备用数据:', error);
      return fallbackData.shareSchedule;
    }
  }

  // 获取所有分享数据
  async getShareList() {
    console.log('获取所有分享数据...');

    try {
      const url = `${this.baseURL}/app_aishare/share/list/`;
      const data = await request(url);
      console.log('分享列表API响应:', data);
      return data;
    } catch (error) {
      console.error('获取分享列表失败，使用备用数据:', error);
      return fallbackData.shareSchedule;
    }
  }

  // 提交分享报名
  async submitShareRegistration(data) {
    console.log('提交分享报名:', data);

    try {
      const url = `${this.baseURL}/app_aishare/share/`;
      const response = await request(url, {
        method: 'POST',
        body: JSON.stringify(data)
      });
      console.log('分享报名API响应:', response);
      return response;
    } catch (error) {
      console.error('提交分享报名失败:', error);
      // API失败时返回错误信息
      throw new Error(error.message || '报名提交失败，请稍后重试');
    }
  }

  // 获取用户详细信息
  async getUserDetails(userId) {
    await this.delay();
    const user = fallbackData.personalLeaderboard.data.find(
      (u) => u.userId === userId
    );
    return user || null;
  }

  // 获取团队详细信息
  async getTeamDetails(teamId) {
    await this.delay();
    const team = fallbackData.teamLeaderboard.data.find(
      (t) => t.teamId === teamId
    );
    return team || null;
  }

  // 刷新排行榜数据
  async refreshLeaderboard() {
    await this.delay();
    return {
      personal: fallbackData.personalLeaderboard,
      team: fallbackData.teamLeaderboard
    };
  }

  // 获取分享列表
  async getShares(page = 1, pageSize = 10) {
    await this.delay();
    return {
      data: fallbackData.shareSchedule,
      total: fallbackData.shareSchedule.length,
      page,
      pageSize
    };
  }

  // 获取分享日历
  async getShareCalendar(year, month) {
    await this.delay();
    return fallbackData.shareSchedule.filter((item) => {
      const date = new Date(item.date_range);
      return date.getFullYear() === year && date.getMonth() + 1 === month;
    });
  }

  // 获取积分历史
  async getPointsHistory(userId, page = 1, pageSize = 20) {
    await this.delay();
    return {
      data: [
        {
          id: 1,
          date: '2024-06-15',
          points: 25,
          reason: '分享一次+提前报名',
          type: 'earn'
        },
        {
          id: 2,
          date: '2024-06-10',
          points: 20,
          reason: '优质案例奖励',
          type: 'earn'
        },
        {
          id: 3,
          date: '2024-06-05',
          points: 15,
          reason: '提前报名奖励',
          type: 'earn'
        }
      ],
      total: 3,
      page,
      pageSize
    };
  }

  // 获取奖品列表
  async getRewards(page = 1, pageSize = 20) {
    await this.delay();
    return {
      data: [
        { id: 1, name: '蓝牙耳机', points: 500, stock: 10 },
        { id: 2, name: '手机支架', points: 200, stock: 20 },
        { id: 3, name: '咖啡券', points: 100, stock: 50 }
      ],
      total: 3,
      page,
      pageSize
    };
  }

  // 获取当前用户信息
  async getCurrentUser() {
    await this.delay();
    return {
      id: 1,
      name: '李琦(七小贝)',
      team: '技术创新队',
      points: 285,
      rank: 1
    };
  }

  // 获取统计数据
  async getStatistics() {
    await this.delay();
    return {
      totalUsers: 50,
      totalTeams: 10,
      totalShares: 120,
      totalPoints: 15000
    };
  }

  // 测试API连接
  async testConnection() {
    await this.delay(200);
    return {
      status: 'success',
      message: 'API连接正常',
      timestamp: new Date().toISOString(),
      baseURL: this.baseURL
    };
  }
}

// 创建API服务实例
const apiService = new ApiService();

export default apiService;
