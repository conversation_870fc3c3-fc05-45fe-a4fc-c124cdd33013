import './index.less';
/**
 * @description 通用文本详情组件
 * @category 组件
 * @component Descriptions
 * @image https://file.ljcdn.com/nebula/desc-example_1725336620722.png
 * @param {Array} list 获取列表数据
 * @param {string | (() => React.ReactNode) | React.ReactNode} title 标题
 * @param {string} className 自定义类名
 * @param {object} style 渲染已选列表内容
 * @param {boolean} [showColon=true] label后是否展示冒号
 * @returns
 */

const prefixCls = 'descriptions';

function Descriptions(props) {
  const { className, style = {}, showColon = true, title, list } = props || {};

  return (
    <div className={`${prefixCls} ${className}`} style={style}>
      {title ? (
        <div className={`${prefixCls}-header`}>
          {typeof title === 'function' ? title() : title}
        </div>
      ) : null}
      <div className={`${prefixCls}-body`}>
        {list && list.length ? (
          <ul className={`${prefixCls}-list`}>
            {list.map((descItem, index) => {
              const { label, value, className: itemClassName = '' } = descItem;
              return (
                <div
                  key={index}
                  className={`${prefixCls}-item ${itemClassName}`}
                >
                  {label ? (
                    <span className={`${prefixCls}-item-label`}>
                      {typeof label === 'function' ? label() : label}
                      {showColon && label ? '：' : ''}
                    </span>
                  ) : null}
                  {value ? (
                    <span className={`${prefixCls}-item-value`}>
                      {typeof value === 'function' ? value() : value}
                    </span>
                  ) : null}
                </div>
              );
            })}
          </ul>
        ) : null}
      </div>
    </div>
  );
}

export default Descriptions;
