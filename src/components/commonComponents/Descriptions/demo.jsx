import Descriptions from './index';

function CompViewer() {
  const list = [
    {
      label: '批次编号',
      value:
        '2278678987227867898722786789872278678987227867898722786789872278678987'
    },
    {
      label: '发票税点百分比',
      value: '2%'
    },
    {
      label: '批次金额',
      value: 300
    },
    {
      label: '开票金额',
      value: 300,
      className: 'ticket-count'
    },
    {
      label: '税差',
      value: <span>178999元</span>
    }
  ];
  return <Descriptions title="Descriptions示例" list={list} />;
}

export default CompViewer;
