const path = require('path');
const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

// 区分环境
const _mode = process.env.NODE_ENV || ' development';
const CDNMap = {
  development: '/',
  test: 'http://test-s1.ljcdn.lianjia.com/fenzu-frontend/', // 请将ke-react-template替换为测试环境submodule
  preview: '//preview-s1.ljcdn.lianjia.com/xxx/', // xxx为预发环境submodule
  production: '//s1.ljcdn.com/xxx/', // xxxxx为线上环境submodule
};

//入口配置
const entry = {
  index: path.resolve(__dirname, '../src/index.jsx')
};

//各个入口对应模板（兼容多页应用）
const htmlWebpackPlugins = Object.keys(entry).map((key) =>
  new HtmlWebpackPlugin({
    favicon: path.resolve(__dirname, '../favicon.ico'),
    template: path.resolve(__dirname, '../public/index.html'), // 使用自定义模板
    filename: `${key}.html`,
    chunks: [key],
  }),
) || [];

module.exports = {
  entry,
  output: {
    path: path.resolve(__dirname, '../dist'), // 打包后的代码放在dist目录下
    filename: '[name].[hash:8].js', // 输出文件名
    publicPath: CDNMap[_mode],
  },
  resolve: {
    fullySpecified: false,
    extensions: ['.js', '.jsx', '.less', '.css', '.scss', '.*'], // 注意顺序，重要的在前
    alias: {
      src: path.resolve(__dirname, '../src'),
    },
  },
  module: {
    rules: [
      { test: /\.(js|jsx|ts|tsx)$/, loader: 'swc-loader', exclude: /node_modules/ },
      {
        test: /\.(png|jpe?g|gif)$/,
        type: 'asset',
        generator: {
          filename: 'statics/[name].[hash:8][ext]',
        },
      },
      {
        test: /\.(woff|woff2|ttf|eot|svg|otf)$/,
        type: 'asset',
        generator: {
          filename: `statics/[name].[hash:8][ext]`,
        },
      },
    ],
  },
  plugins: [
    ...htmlWebpackPlugins,
    new MiniCssExtractPlugin({
      filename: `css/[name].[contenthash].css`,
      ignoreOrder: true,
    }),
    new webpack.DefinePlugin({
      'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV), // 注入环境变量，在代码中通过process.env.NODE_ENV获取
      'BASE_URL': JSON.stringify(CDNMap[_mode]), // 注入环境变量，在代码中通过BASE_URL获取
    }),
  ],
};
