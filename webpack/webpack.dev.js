const path = require('path');
const webpack = require('webpack');
const { merge } = require('webpack-merge');
const baseConfig = require('./webpack.base.js');
const ProgressBarPlugin = require('progress-bar-webpack-plugin');

const sysLink = 'http://sparks.ttb.test.ke.com'; //本地访问后端域名

module.exports = merge(baseConfig, {
  mode: 'development', // 开发模式
  devServer: {
    host: '0.0.0.0', //服务器ip地址
    port: 3007, //端口
    historyApiFallback: true, //请求不到资源时返回入口html文件，兼容各种页面路由请求（单页应用配置）
    // historyApiFallback: { //多页应用使用该配置（不需要可删除）
    //   rewrites: [
    //     { from: /^\/$/, to: '/index.html' },
    //     { from: /^\/demo$/, to: '/demo.html' }
    //   ]
    // },
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
    compress: true,
    allowedHosts: 'all',
    open: false, //编译完不自动打开浏览器
    proxy: [
      {
        context: [
          '/api',
          // keboot登录组件提供的固定接口，可根据需要取消注释
          // '/login/cas',
          // '/loginUser/info',
          // '/logout',
        ],
        target: sysLink, // 代理到的目标地址
        pathRewrite: {
          '^/api': '/', // 重写路径
        },
        changeOrigin: true, //避免跨域问题
      },
      {
        context: ['/app_aishare'],
        target: 'http://sparks.ttb.test.ke.com', // 代理到测试环境API服务器
        changeOrigin: true,
        secure: false,
        logLevel: 'debug',
        onProxyReq: (proxyReq, req, res) => {
          console.log('代理请求:', req.method, req.url, '-> http://sparks.ttb.test.ke.com' + req.url);
        },
        onProxyRes: (proxyRes, req, res) => {
          console.log('代理响应:', proxyRes.statusCode, req.url);
        }
      },
    ],
  },
  module: {
    rules: [
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader'],
      },
      {
        test: /\.scss$/,
        use: ['style-loader', 'css-loader', 'sass-loader'],
      },
      {
        test: /\.less$/,
        use: ['style-loader', 'css-loader', 'less-loader'],
      },
      {
        test: /\.styl$/,
        use: ['style-loader', 'css-loader', 'stylus-loader'],
      },
    ],
  },
  plugins: [
    new webpack.HotModuleReplacementPlugin(), //启用热更新
    new ProgressBarPlugin(), //构建进度条
  ],
});
